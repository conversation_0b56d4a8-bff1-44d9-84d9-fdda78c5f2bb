package com.wexl.erp.fees.service;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.repository.StudentAttributeValueRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeDueReportService {

  private final FeeHeadRepository feeHeadRepository;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;
  private final StudentAttributeValueRepository studentAttributeValueRepository;
  private final GuardianService guardianService;

  public void generateFeeDueReportCsv(
          String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {

    FeeDto.FeeDueReportResponse reportData = generateFeeDueReport(orgSlug, request);
    String reportType = request.reportType() != null ? request.reportType() : "total_due";

    if ("past_due".equalsIgnoreCase(reportType)) {
      generatePastDueReportCsv(reportData.pastDueReport(), response, request);
    } else {
      generateTotalDueReportCsv(reportData.totalDueReport(), response, request);
    }
  }

  private Double calculateDiscount(Student student) {
    var feeHeadList = feeHeadRepository.findAllByStudent(student);
    return (feeHeadList == null || feeHeadList.isEmpty())
        ? 0.0
        : feeHeadList.stream()
            .map(FeeHead::getDiscountAmount)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();
  }

  public List<Student> getStudentsBySectionUuids(FeeDto.FeeDueReportRequest request) {
    List<Section> sections =
        sectionRepository.findAllByUuidIn(
            request.sectionUuids().stream().map(UUID::fromString).toList());
    List<Student> students = new ArrayList<>();
    for (Section section : sections) {
      List<Student> sectionStudents = studentRepository.getStudentsBySection(section);
      students.addAll(sectionStudents);
    }
    return students;
  }

  private String getFileName(String reportType) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    return switch (reportType) {
      case "past_due" -> "past_due_report_" + timestamp + ".csv";
      case "total_due" -> "total_due_report_" + timestamp + ".csv";
      default -> "fee_due_report_" + timestamp + ".csv";
    };
  }

  private String getStudentAttribute(Student student, String attributeName) {
    List<StudentAttributeValueModel> attributes =
        studentAttributeValueRepository.findAllByStudentId(student.getId());
    return attributes.stream()
        .filter(attr -> attributeName.equals(attr.getAttributeDefinition().getName()))
        .map(StudentAttributeValueModel::getValue)
        .findFirst()
        .orElse("");
  }

  private Guardian getGuardianInfo(Student student, GuardianRole role) {
    return student.getGuardians().stream()
        .filter(guardian -> role.equals(guardian.getRelationType()))
        .findFirst()
        .orElse(null);
  }

  private String getDndStatus(Student student) {
    String dndStatus = getStudentAttribute(student, "phone_number");
    return dndStatus.isEmpty() ? "NO" : "YES";
  }

  public FeeDto.FeeDueReportResponse generateFeeDueReport(
      String orgSlug, FeeDto.FeeDueReportRequest request) {

    List<Student> students = getStudentsBySectionUuids(request);

    List<FeeHead> totalDueFeeHeads = feeHeadRepository.findTotalDueFeeDetails(
        orgSlug,
        students.stream().map(Student::getId).collect(Collectors.toList()),
        dateTimeUtil.convertEpochToIso8601(request.fromDate()),
        dateTimeUtil.convertEpochToIso8601(request.toDate()));

    List<FeeHead> pastDueFeeHeads = feeHeadRepository.findPastDueFeeDetails(
        orgSlug,
        students.stream().map(Student::getId).collect(Collectors.toList()),
        dateTimeUtil.convertEpochToIso8601(request.fromDate()),
        dateTimeUtil.convertEpochToIso8601(request.toDate()));

    Map<Student, List<FeeHead>> totalDueFeeHeadsByStudent =
        totalDueFeeHeads.stream().collect(Collectors.groupingBy(FeeHead::getStudent));
    Map<Student, List<FeeHead>> pastDueFeeHeadsByStudent =
        pastDueFeeHeads.stream().collect(Collectors.groupingBy(FeeHead::getStudent));

    List<FeeDto.TotalDueReportResponse> totalDueReports = students.stream()
        .map(student -> buildTotalDueReportResponse(student, totalDueFeeHeadsByStudent.get(student)))
        .filter(Objects::nonNull)
        .collect(Collectors.toList());

    List<FeeDto.PastDueReportResponse> pastDueReports = students.stream()
        .map(student -> buildPastDueReportResponse(student, pastDueFeeHeadsByStudent.get(student)))
        .filter(Objects::nonNull)
        .collect(Collectors.toList());

    return FeeDto.FeeDueReportResponse.builder()
        .totalDueReport(totalDueReports)
        .pastDueReport(pastDueReports)
        .build();
  }

  private FeeDto.TotalDueReportResponse buildTotalDueReportResponse(Student student, List<FeeHead> feeHeads) {
    if (feeHeads == null) {
      feeHeads = new ArrayList<>();
    }

    Guardian father = getGuardianInfo(student, GuardianRole.FATHER);
    Guardian mother = getGuardianInfo(student, GuardianRole.MOTHER);

    Map<String, Double> feeAmounts = initializeTotalDueFeeAmounts();

    processFeeHeadsForTotalDue(feeHeads, feeAmounts);

    Double totalFeeAssigned = feeHeads.stream()
        .mapToDouble(fh -> fh.getAmount() != null ? fh.getAmount() : 0.0)
        .sum();

    Double totalPaid = feeHeads.stream()
        .mapToDouble(fh -> fh.getPaidAmount() != null ? fh.getPaidAmount() : 0.0)
        .sum();

    Double totalDue = feeHeads.stream()
        .mapToDouble(fh -> fh.getBalanceAmount() != null ? fh.getBalanceAmount() : 0.0)
        .sum();

    Double concessionAmount = calculateDiscount(student);

    return FeeDto.TotalDueReportResponse.builder()
        .studentName(userService.getNameByUserInfo(student.getUserInfo()))
        .admissionNumber(student.getUserInfo().getUserName())
        .rollNumber(student.getClassRollNumber())
        .className(student.getSection() != null ? student.getSection().getName() : "")
        .studentStatus(student.getActive() != null && student.getActive() == '1' ? "Active" : "Inactive")

        .fatherName(father != null ? guardianService.getGuardianName(father) : null)
        .fatherMobile(father != null ? father.getMobileNumber() : null)
        .fatherEmail(father != null ? father.getEmail() : null)
        .motherName(mother != null ? guardianService.getGuardianName(mother) : null)
        .motherMobile(mother != null ? mother.getMobileNumber() : null)
        .motherEmail(mother != null ? mother.getEmail() : null)

        .dndStatus(getDndStatus(student))
        .dateOfAdmission(student.getCreatedAt() != null
            ? student.getCreatedAt().toLocalDateTime().format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
            : "")

        .admissionFee(feeAmounts.get("admission_fee"))
        .tuitionAdmissionFee(feeAmounts.get("tuition_admission_fee"))
        .tuitionTerm1(feeAmounts.get("tuition_term1"))
        .tuitionTerm2(feeAmounts.get("tuition_term2"))
        .tuitionTerm3(feeAmounts.get("tuition_term3"))
        .tuitionTerm4(feeAmounts.get("tuition_term4"))
        .tuitionApr(feeAmounts.get("tuition_apr"))
        .tuitionMay(feeAmounts.get("tuition_may"))
        .tuitionJun(feeAmounts.get("tuition_jun"))
        .tuitionJul(feeAmounts.get("tuition_jul"))
        .tuitionAug(feeAmounts.get("tuition_aug"))
        .tuitionSep(feeAmounts.get("tuition_sep"))
        .tuitionOct(feeAmounts.get("tuition_oct"))
        .tuitionNov(feeAmounts.get("tuition_nov"))
        .tuitionDec(feeAmounts.get("tuition_dec"))
        .tuitionJan(feeAmounts.get("tuition_jan"))
        .tuitionFeb(feeAmounts.get("tuition_feb"))
        .tuitionMar(feeAmounts.get("tuition_mar"))
        .transportApr(feeAmounts.get("transport_apr"))
        .transportMay(feeAmounts.get("transport_may"))
        .transportJun(feeAmounts.get("transport_jun"))
        .transportJul(feeAmounts.get("transport_jul"))
        .transportAug(feeAmounts.get("transport_aug"))
        .transportSep(feeAmounts.get("transport_sep"))
        .transportOct(feeAmounts.get("transport_oct"))
        .transportNov(feeAmounts.get("transport_nov"))
        .transportDec(feeAmounts.get("transport_dec"))
        .transportJan(feeAmounts.get("transport_jan"))
        .transportFeb(feeAmounts.get("transport_feb"))
        .transportMar(feeAmounts.get("transport_mar"))
        .tuitionLateFeTerm1(feeAmounts.get("tuition_late_fee_term1"))
        .tuitionLateFeTerm2(feeAmounts.get("tuition_late_fee_term2"))
        .tuitionLateFeTerm3(feeAmounts.get("tuition_late_fee_term3"))
        .tuitionLateFeTerm4(feeAmounts.get("tuition_late_fee_term4"))
        .tuitionLateFeeApr(feeAmounts.get("tuition_late_fee_apr"))
        .tuitionLateFeeMay(feeAmounts.get("tuition_late_fee_may"))
        .tuitionLateFeeJun(feeAmounts.get("tuition_late_fee_jun"))
        .tuitionLateFeeJul(feeAmounts.get("tuition_late_fee_jul"))
        .tuitionLateFeeAug(feeAmounts.get("tuition_late_fee_aug"))
        .tuitionLateFeeSep(feeAmounts.get("tuition_late_fee_sep"))
        .tuitionLateFeeOct(feeAmounts.get("tuition_late_fee_oct"))
        .tuitionLateFeeNov(feeAmounts.get("tuition_late_fee_nov"))
        .tuitionLateFeeDec(feeAmounts.get("tuition_late_fee_dec"))
        .tuitionLateFeeJan(feeAmounts.get("tuition_late_fee_jan"))
        .tuitionLateFeeFeb(feeAmounts.get("tuition_late_fee_feb"))
        .tuitionLateFeeMar(feeAmounts.get("tuition_late_fee_mar"))
        .transportLateFeeApr(feeAmounts.get("transport_late_fee_apr"))
        .transportLateFeeMay(feeAmounts.get("transport_late_fee_may"))
        .transportLateFeeJun(feeAmounts.get("transport_late_fee_jun"))
        .transportLateFeeJul(feeAmounts.get("transport_late_fee_jul"))
        .transportLateFeeAug(feeAmounts.get("transport_late_fee_aug"))
        .transportLateFeeSep(feeAmounts.get("transport_late_fee_sep"))
        .transportLateFeeOct(feeAmounts.get("transport_late_fee_oct"))
        .transportLateFeeNov(feeAmounts.get("transport_late_fee_nov"))
        .transportLateFeeDec(feeAmounts.get("transport_late_fee_dec"))
        .transportLateFeeJan(feeAmounts.get("transport_late_fee_jan"))
        .transportLateFeeFeb(feeAmounts.get("transport_late_fee_feb"))
        .transportLateFeeMar(feeAmounts.get("transport_late_fee_mar"))
        .delete("")
        .extendedDayCareLateFeee(feeAmounts.get("extended_day_care_late_fee"))
        .extendedDayCare(feeAmounts.get("extended_day_care"))

        .totalFeeAssigned(totalFeeAssigned)
        .concessionAmount(concessionAmount)
        .totalPaid(totalPaid)
        .totalDue(totalDue)
        .feeRemark("")
        .build();
  }

  private Map<String, Double> initializeTotalDueFeeAmounts() {
    Map<String, Double> feeAmounts = new HashMap<>();

    feeAmounts.put("admission_fee", 0.0);
    feeAmounts.put("tuition_admission_fee", 0.0);
    feeAmounts.put("tuition_term1", 0.0);
    feeAmounts.put("tuition_term2", 0.0);
    feeAmounts.put("tuition_term3", 0.0);
    feeAmounts.put("tuition_term4", 0.0);

    String[] months = {"apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec", "jan", "feb", "mar"};
    for (String month : months) {
      feeAmounts.put("tuition_" + month, 0.0);
      feeAmounts.put("transport_" + month, 0.0);
      feeAmounts.put("tuition_late_fee_" + month, 0.0);
      feeAmounts.put("transport_late_fee_" + month, 0.0);
    }

    for (int i = 1; i <= 4; i++) {
      feeAmounts.put("tuition_late_fee_term" + i, 0.0);
    }

    feeAmounts.put("extended_day_care", 0.0);
    feeAmounts.put("extended_day_care_late_fee", 0.0);

    return feeAmounts;
  }

  private void processFeeHeadsForTotalDue(List<FeeHead> feeHeads, Map<String, Double> feeAmounts) {
    for (FeeHead feeHead : feeHeads) {
      String feeTypeName = feeHead.getFeeType().getDescription().toLowerCase();
      String month = "";

      if (feeHead.getDueDate() != null) {
        month = feeHead.getDueDate().format(DateTimeFormatter.ofPattern("MMM")).toLowerCase();
      }

      Double balanceAmount = feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0;
      Double fineAmount = feeHead.getFineAmount() != null ? feeHead.getFineAmount() : 0.0;

      if (feeTypeName.contains("admission")) {
        if (feeTypeName.contains("tuition")) {
          feeAmounts.merge("tuition_admission_fee", balanceAmount, Double::sum);
        } else {
          feeAmounts.merge("admission_fee", balanceAmount, Double::sum);
        }
      } else if (feeTypeName.contains("tuition")) {
        if (feeTypeName.contains("term")) {
          if (feeTypeName.contains("term 1") || feeTypeName.contains("term1")) {
            feeAmounts.merge("tuition_term1", balanceAmount, Double::sum);
            feeAmounts.merge("tuition_late_fee_term1", fineAmount, Double::sum);
          } else if (feeTypeName.contains("term 2") || feeTypeName.contains("term2")) {
            feeAmounts.merge("tuition_term2", balanceAmount, Double::sum);
            feeAmounts.merge("tuition_late_fee_term2", fineAmount, Double::sum);
          } else if (feeTypeName.contains("term 3") || feeTypeName.contains("term3")) {
            feeAmounts.merge("tuition_term3", balanceAmount, Double::sum);
            feeAmounts.merge("tuition_late_fee_term3", fineAmount, Double::sum);
          } else if (feeTypeName.contains("term 4") || feeTypeName.contains("term4")) {
            feeAmounts.merge("tuition_term4", balanceAmount, Double::sum);
            feeAmounts.merge("tuition_late_fee_term4", fineAmount, Double::sum);
          }
        } else if (!month.isEmpty()) {
          feeAmounts.merge("tuition_" + month, balanceAmount, Double::sum);
          feeAmounts.merge("tuition_late_fee_" + month, fineAmount, Double::sum);
        }
      } else if (feeTypeName.contains("transport")) {
        if (!month.isEmpty()) {
          feeAmounts.merge("transport_" + month, balanceAmount, Double::sum);
          feeAmounts.merge("transport_late_fee_" + month, fineAmount, Double::sum);
        }
      } else if (feeTypeName.contains("extended day care") || feeTypeName.contains("daycare")) {
        feeAmounts.merge("extended_day_care", balanceAmount, Double::sum);
        feeAmounts.merge("extended_day_care_late_fee", fineAmount, Double::sum);
      }
    }
  }

  private FeeDto.PastDueReportResponse buildPastDueReportResponse(Student student, List<FeeHead> feeHeads) {
    if (feeHeads == null) {
      feeHeads = new ArrayList<>();
    }

    Guardian father = getGuardianInfo(student, GuardianRole.FATHER);
    Guardian mother = getGuardianInfo(student, GuardianRole.MOTHER);

    Map<String, Double> feeAmounts = initializePastDueFeeAmounts();

    processFeeHeadsForPastDue(feeHeads, feeAmounts);

    Double totalFeeAssignedTillDate = feeHeads.stream()
        .mapToDouble(fh -> fh.getAmount() != null ? fh.getAmount() : 0.0)
        .sum();

    Double totalPaidTillDate = feeHeads.stream()
        .mapToDouble(fh -> fh.getPaidAmount() != null ? fh.getPaidAmount() : 0.0)
        .sum();

    Double totalDue = feeHeads.stream()
        .mapToDouble(fh -> fh.getBalanceAmount() != null ? fh.getBalanceAmount() : 0.0)
        .sum();

    Double concessionAmount = calculateDiscount(student);

    return FeeDto.PastDueReportResponse.builder()
        .studentName(userService.getNameByUserInfo(student.getUserInfo()))
        .admissionNumber(student.getUserInfo().getUserName())
        .rollNumber(student.getClassRollNumber())
        .className(student.getSection() != null ? student.getSection().getName() : "")
        .studentStatus(student.getActive() != null && student.getActive() == '1' ? "Active" : "Inactive")
        .fatherName(father != null ? guardianService.getGuardianName(father) : null)
        .fatherMobile(father != null ? father.getMobileNumber() : null)
        .fatherEmail(father != null ? father.getEmail() : null)
        .motherName(mother != null ? guardianService.getGuardianName(mother) : null)
        .motherMobile(mother != null ? mother.getMobileNumber() : null)
        .motherEmail(mother != null ? mother.getEmail() : null)

        .dndStatus(getDndStatus(student))
        .dateOfAdmission(student.getCreatedAt() != null
            ? student.getCreatedAt().toLocalDateTime().format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
            : "")

        .admissionFee(feeAmounts.get("admission_fee"))
        .tuitionAdmissionFee(feeAmounts.get("tuition_admission_fee"))
        .tuitionTerm1(feeAmounts.get("tuition_term1"))
        .tuitionTerm2(feeAmounts.get("tuition_term2"))
        .transportApr(feeAmounts.get("transport_apr"))
        .transportMay(feeAmounts.get("transport_may"))
        .transportJun(feeAmounts.get("transport_jun"))
        .transportJul(feeAmounts.get("transport_jul"))
        .transportAug(feeAmounts.get("transport_aug"))
        .extendedDayCare(feeAmounts.get("extended_day_care"))
        .delete("")
            .totalFeeAssignedTillDate(totalFeeAssignedTillDate)
        .concessionAmount(concessionAmount)
        .totalPaidTillDate(totalPaidTillDate)
        .totalDue(totalDue)
        .feeRemark("")
        .build();
  }

  private Map<String, Double> initializePastDueFeeAmounts() {
    Map<String, Double> feeAmounts = new HashMap<>();

    feeAmounts.put("admission_fee", 0.0);
    feeAmounts.put("tuition_admission_fee", 0.0);
    feeAmounts.put("tuition_term1", 0.0);
    feeAmounts.put("tuition_term2", 0.0);
    feeAmounts.put("transport_apr", 0.0);
    feeAmounts.put("transport_may", 0.0);
    feeAmounts.put("transport_jun", 0.0);
    feeAmounts.put("transport_jul", 0.0);
    feeAmounts.put("transport_aug", 0.0);
    feeAmounts.put("extended_day_care", 0.0);

    return feeAmounts;
  }

  private void processFeeHeadsForPastDue(List<FeeHead> feeHeads, Map<String, Double> feeAmounts) {
    for (FeeHead feeHead : feeHeads) {
      String feeTypeName = feeHead.getFeeType().getDescription().toLowerCase();
      String month = "";

      if (feeHead.getDueDate() != null) {
        month = feeHead.getDueDate().format(DateTimeFormatter.ofPattern("MMM")).toLowerCase();
      }

      Double balanceAmount = feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0;

      if (feeTypeName.contains("admission")) {
        if (feeTypeName.contains("tuition")) {
          feeAmounts.merge("tuition_admission_fee", balanceAmount, Double::sum);
        } else {
          feeAmounts.merge("admission_fee", balanceAmount, Double::sum);
        }
      } else if (feeTypeName.contains("tuition")) {
        if (feeTypeName.contains("term 1") || feeTypeName.contains("term1")) {
          feeAmounts.merge("tuition_term1", balanceAmount, Double::sum);
        } else if (feeTypeName.contains("term 2") || feeTypeName.contains("term2")) {
          feeAmounts.merge("tuition_term2", balanceAmount, Double::sum);
        }
      } else if (feeTypeName.contains("transport")) {
        if ("apr".equals(month)) {
          feeAmounts.merge("transport_apr", balanceAmount, Double::sum);
        } else if ("may".equals(month)) {
          feeAmounts.merge("transport_may", balanceAmount, Double::sum);
        } else if ("jun".equals(month)) {
          feeAmounts.merge("transport_jun", balanceAmount, Double::sum);
        } else if ("jul".equals(month)) {
          feeAmounts.merge("transport_jul", balanceAmount, Double::sum);
        } else if ("aug".equals(month)) {
          feeAmounts.merge("transport_aug", balanceAmount, Double::sum);
        }
      } else if (feeTypeName.contains("extended day care") || feeTypeName.contains("daycare")) {
        feeAmounts.merge("extended_day_care", balanceAmount, Double::sum);
      }
    }
  }

  private void generateTotalDueReportCsv(
      List<FeeDto.TotalDueReportResponse> reportData,
      HttpServletResponse response,
      FeeDto.FeeDueReportRequest request) {

    String fileName = getFileName(request.reportType());
    response.setContentType("text/csv");
    response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

    List<String> headers = getTotalDueReportHeaders();
    List<List<String>> csvBody = buildTotalDueReportCsvBody(reportData);

    CsvUtils.generateCsv(headers.toArray(new String[0]), csvBody, response);
  }

  private void generatePastDueReportCsv(
      List<FeeDto.PastDueReportResponse> reportData,
      HttpServletResponse response,
      FeeDto.FeeDueReportRequest request) {

    String fileName = getFileName(request.reportType());
    response.setContentType("text/csv");
    response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

    List<String> headers = getPastDueReportHeaders();
    List<List<String>> csvBody = buildPastDueReportCsvBody(reportData);

    CsvUtils.generateCsv(headers.toArray(new String[0]), csvBody, response);
  }

  private List<String> getTotalDueReportHeaders() {
    return List.of(
        "STUDENT NAME", "Admission Number", "Roll No.", "CLASS", "Student status",
        "Father Name", "Father Mobile", "Father Email", "Mother Name", "Mother Mobile", "Mother EMail",
        "DND status", "Date of Adm#", "Student Category", "Hostel Category",
        "Admission Fee",

        "Admission Fee", "Term 1", "Term 2", "Term 3", "Term 4",
        "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC", "JAN", "FEB", "MAR",

        "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC", "JAN", "FEB", "MAR",

        "Term 1", "Term 2", "Term 3", "Term 4",
        "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC", "JAN", "FEB", "MAR",

        "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC", "JAN", "FEB", "MAR",
        "Delete", "EXTENDED DAY CARE- LateFee", "Extended Day Care",
        "Total Fee Assigned", "Concession Amount", "Total Paid", "Total Due", "Fee Remark"
    );
  }

  private List<String> getPastDueReportHeaders() {
    return List.of(
        "STUDENT NAME", "Admission Number", "Roll No.", "CLASS", "Student status",
        "Father Name", "Father Mobile", "Father Email", "Mother Name", "Mother Mobile", "Mother EMail",
        "DND status", "Date of Adm#", "Student Category", "Hostel Category",
        "Admission Fee",

        "Admission Fee", "Term 1", "Term 2",
        "APR", "MAY", "JUN", "JUL", "AUG",
        "Extended Day Care",
        "Delete",
        "Total Fee Assigned till Date", "Concession Amount", "Total Paid till Date", "Total Due", "Fee Remark"
    );
  }

  private List<List<String>> buildTotalDueReportCsvBody(List<FeeDto.TotalDueReportResponse> reportData) {
    List<List<String>> csvBody = new ArrayList<>();

    for (FeeDto.TotalDueReportResponse report : reportData) {
      List<String> row = new ArrayList<>();


      row.add(report.studentName() != null ? report.studentName() : "");
      row.add(report.admissionNumber() != null ? report.admissionNumber() : "");
      row.add(report.rollNumber() != null ? report.rollNumber() : "");
      row.add(report.className() != null ? report.className() : "");
      row.add(report.studentStatus() != null ? report.studentStatus() : "");


      row.add(report.fatherName() != null ? report.fatherName() : "");
      row.add(report.fatherMobile() != null ? report.fatherMobile() : "");
      row.add(report.fatherEmail() != null ? report.fatherEmail() : "");
      row.add(report.motherName() != null ? report.motherName() : "");
      row.add(report.motherMobile() != null ? report.motherMobile() : "");
      row.add(report.motherEmail() != null ? report.motherEmail() : "");


      row.add(report.dndStatus() != null ? report.dndStatus() : "");
      row.add(report.dateOfAdmission() != null ? report.dateOfAdmission() : "");
      row.add(report.studentCategory() != null ? report.studentCategory() : "");
      row.add(report.hostelCategory() != null ? report.hostelCategory() : "");

      row.add(formatAmount(report.admissionFee()));
      row.add(formatAmount(report.tuitionAdmissionFee()));
      row.add(formatAmount(report.tuitionTerm1()));
      row.add(formatAmount(report.tuitionTerm2()));
      row.add(formatAmount(report.tuitionTerm3()));
      row.add(formatAmount(report.tuitionTerm4()));
      row.add(formatAmount(report.tuitionApr()));
      row.add(formatAmount(report.tuitionMay()));
      row.add(formatAmount(report.tuitionJun()));
      row.add(formatAmount(report.tuitionJul()));
      row.add(formatAmount(report.tuitionAug()));
      row.add(formatAmount(report.tuitionSep()));
      row.add(formatAmount(report.tuitionOct()));
      row.add(formatAmount(report.tuitionNov()));
      row.add(formatAmount(report.tuitionDec()));
      row.add(formatAmount(report.tuitionJan()));
      row.add(formatAmount(report.tuitionFeb()));
      row.add(formatAmount(report.tuitionMar()));

      row.add(formatAmount(report.transportApr()));
      row.add(formatAmount(report.transportMay()));
      row.add(formatAmount(report.transportJun()));
      row.add(formatAmount(report.transportJul()));
      row.add(formatAmount(report.transportAug()));
      row.add(formatAmount(report.transportSep()));
      row.add(formatAmount(report.transportOct()));
      row.add(formatAmount(report.transportNov()));
      row.add(formatAmount(report.transportDec()));
      row.add(formatAmount(report.transportJan()));
      row.add(formatAmount(report.transportFeb()));
      row.add(formatAmount(report.transportMar()));

      row.add(formatAmount(report.tuitionLateFeTerm1()));
      row.add(formatAmount(report.tuitionLateFeTerm2()));
      row.add(formatAmount(report.tuitionLateFeTerm3()));
      row.add(formatAmount(report.tuitionLateFeTerm4()));
      row.add(formatAmount(report.tuitionLateFeeApr()));
      row.add(formatAmount(report.tuitionLateFeeMay()));
      row.add(formatAmount(report.tuitionLateFeeJun()));
      row.add(formatAmount(report.tuitionLateFeeJul()));
      row.add(formatAmount(report.tuitionLateFeeAug()));
      row.add(formatAmount(report.tuitionLateFeeSep()));
      row.add(formatAmount(report.tuitionLateFeeOct()));
      row.add(formatAmount(report.tuitionLateFeeNov()));
      row.add(formatAmount(report.tuitionLateFeeDec()));
      row.add(formatAmount(report.tuitionLateFeeJan()));
      row.add(formatAmount(report.tuitionLateFeeFeb()));
      row.add(formatAmount(report.tuitionLateFeeMar()));

      row.add(formatAmount(report.transportLateFeeApr()));
      row.add(formatAmount(report.transportLateFeeMay()));
      row.add(formatAmount(report.transportLateFeeJun()));
      row.add(formatAmount(report.transportLateFeeJul()));
      row.add(formatAmount(report.transportLateFeeAug()));
      row.add(formatAmount(report.transportLateFeeSep()));
      row.add(formatAmount(report.transportLateFeeOct()));
      row.add(formatAmount(report.transportLateFeeNov()));
      row.add(formatAmount(report.transportLateFeeDec()));
      row.add(formatAmount(report.transportLateFeeJan()));
      row.add(formatAmount(report.transportLateFeeFeb()));
      row.add(formatAmount(report.transportLateFeeMar()));

      row.add(report.delete() != null ? report.delete() : "");
      row.add(formatAmount(report.extendedDayCareLateFeee()));
      row.add(formatAmount(report.extendedDayCare()));

      row.add(formatAmount(report.totalFeeAssigned()));
      row.add(formatAmount(report.concessionAmount()));
      row.add(formatAmount(report.totalPaid()));
      row.add(formatAmount(report.totalDue()));
      row.add(report.feeRemark() != null ? report.feeRemark() : "");

      csvBody.add(row);
    }

    return csvBody;
  }

  private List<List<String>> buildPastDueReportCsvBody(List<FeeDto.PastDueReportResponse> reportData) {
    List<List<String>> csvBody = new ArrayList<>();

    for (FeeDto.PastDueReportResponse report : reportData) {
      List<String> row = new ArrayList<>();

      row.add(report.studentName() != null ? report.studentName() : "");
      row.add(report.admissionNumber() != null ? report.admissionNumber() : "");
      row.add(report.rollNumber() != null ? report.rollNumber() : "");
      row.add(report.className() != null ? report.className() : "");
      row.add(report.studentStatus() != null ? report.studentStatus() : "");

      row.add(report.fatherName() != null ? report.fatherName() : "");
      row.add(report.fatherMobile() != null ? report.fatherMobile() : "");
      row.add(report.fatherEmail() != null ? report.fatherEmail() : "");
      row.add(report.motherName() != null ? report.motherName() : "");
      row.add(report.motherMobile() != null ? report.motherMobile() : "");
      row.add(report.motherEmail() != null ? report.motherEmail() : "");

      row.add(report.dndStatus() != null ? report.dndStatus() : "");
      row.add(report.dateOfAdmission() != null ? report.dateOfAdmission() : "");
      row.add(report.studentCategory() != null ? report.studentCategory() : "");
      row.add(report.hostelCategory() != null ? report.hostelCategory() : "");

      row.add(formatAmount(report.admissionFee()));
      row.add(formatAmount(report.tuitionAdmissionFee()));
      row.add(formatAmount(report.tuitionTerm1()));
      row.add(formatAmount(report.tuitionTerm2()));
      row.add(formatAmount(report.transportApr()));
      row.add(formatAmount(report.transportMay()));
      row.add(formatAmount(report.transportJun()));
      row.add(formatAmount(report.transportJul()));
      row.add(formatAmount(report.transportAug()));
      row.add(formatAmount(report.extendedDayCare()));

      row.add(report.delete() != null ? report.delete() : "");

      row.add(formatAmount(report.totalFeeAssignedTillDate()));
      row.add(formatAmount(report.concessionAmount()));
      row.add(formatAmount(report.totalPaidTillDate()));
      row.add(formatAmount(report.totalDue()));
      row.add(report.feeRemark() != null ? report.feeRemark() : "");

      csvBody.add(row);
    }

    return csvBody;
  }

  private String formatAmount(Double amount) {
    if (amount == null || amount == 0.0) {
      return "0";
    }
    return String.format("%.0f", amount);
  }
}
