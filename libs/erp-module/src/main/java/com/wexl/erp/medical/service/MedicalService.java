package com.wexl.erp.medical.service;

import com.wexl.erp.medical.dto.MedicalProfileDto;
import com.wexl.erp.medical.model.ErpMedicalHistory;
import com.wexl.erp.medical.repository.MedicalRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.guardian.dto.GuardianRequest;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.repository.GuardianRepository;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.telegram.service.UserService;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class MedicalService {

  private final MedicalRepository medicalRepository;
  private final UserRepository userRepository;
  private final UserService userService;
  private final ReportCardService reportCardService;
  private final StudentAuthService studentAuthService;
  private final StudentRepository studentRepository;
  private final GuardianService guardianService;
  private final DateTimeUtil dateTimeUtil;
  private final GuardianRepository guardianRepository;

  public void createMedicalHistory(
      String orgSlug, String studentAuthId, MedicalProfileDto.Request request) {

    ErpMedicalHistory medicalProfile = new ErpMedicalHistory();
    var user = userRepository.findByAuthUserId(studentAuthId).orElseThrow();
    var student = user.getStudentInfo();
    List<Guardian> existingGuardians = guardianRepository.findByStudentId(student.getId());
    Set<String> existingKeys =
        existingGuardians.stream()
            .map(
                g ->
                    (g.getFirstName() + "|" + g.getRelationType()).toLowerCase()
                        + g.getMobileNumber())
            .collect(Collectors.toSet());
    List<Guardian> guardians = new ArrayList<>();
    if (request.guardianDetails() != null
        && !request.guardianDetails().isEmpty()
        && request.guardianDetails().size() > existingGuardians.size()) {
      List<GuardianRequest> guardianRequests =
          request.guardianDetails().stream()
              .filter(
                  guardianData ->
                      !existingKeys.contains(
                          (guardianData.guardianName() + "|" + guardianData.relationType())
                                  .toLowerCase()
                              + guardianData.phoneNumber()))
              .map(
                  guardianData ->
                      GuardianRequest.builder()
                          .firstName(guardianData.guardianName())
                          .lastName("")
                          .emailId("")
                          .relationType(guardianData.relationType())
                          .isPrimary(false)
                          .mobileNumber(String.valueOf(guardianData.phoneNumber()))
                          .build())
              .toList();
      guardianService.createGuardian(studentAuthId, guardianRequests);
    }
    guardians.addAll(guardianRepository.findByStudentId(student.getId()));

    Optional<ErpMedicalHistory> existingMedicalProfile =
        medicalRepository.findByStudentId(student.getId());
    if (existingMedicalProfile.isPresent()) {
      medicalProfile = existingMedicalProfile.get();
    }

    medicalProfile.setStudentName(userService.getNameByUserInfo(user));
    medicalProfile.setStudentId(student.getId());
    medicalProfile.setOrgSlug(orgSlug);
    medicalProfile.setBloodGroup(request.bloodGroup());
    medicalProfile.setHeight(request.height());
    medicalProfile.setWeight(request.weight());
    medicalProfile.setDrugOrMedicines(request.drugOrMedicines());
    medicalProfile.setFoodAllergy(request.foodAllergy());
    medicalProfile.setChronicDiseases(request.chronicDiseases());
    medicalProfile.setHeartCondition(request.heartCondition());
    medicalProfile.setSurgeryOrAdmittedHospital(request.surgeryOrAdmittedHospital());
    medicalProfile.setWearsSpectacles(request.wearsSpectacles());
    medicalProfile.setDentalTreatment(request.dentalTreatment());
    medicalProfile.setAllergies(request.allergies());
    medicalProfile.setIllness(request.illness());
    medicalProfile.setRemarks(request.remarks());
    medicalProfile.setGuardians(guardians);
    medicalRepository.save(medicalProfile);
  }

  public MedicalProfileDto.Response getMedicalHistory(String studentAuthId) {
    var user = userRepository.findByAuthUserId(studentAuthId).orElseThrow();
    Optional<ErpMedicalHistory> medicalProfile =
        medicalRepository.findByStudentId(user.getStudentInfo().getId());
    if (medicalProfile.isEmpty()) {
      return MedicalProfileDto.Response.builder().build();
    }
    return buildMedicalProfileResponse(medicalProfile.get());
  }

  public List<MedicalProfileDto.Response> getMedicalHistoryOfOrg(String orgSlug) {
    var medicalProfiles = medicalRepository.findAllByOrgSlug(orgSlug);
    return medicalProfiles.stream().map(this::buildMedicalProfileResponse).toList();
  }

  private MedicalProfileDto.Response buildMedicalProfileResponse(ErpMedicalHistory medicalProfile) {
    var student = studentRepository.findById(medicalProfile.getStudentId()).orElseThrow();
    return MedicalProfileDto.Response.builder()
        .studentId(medicalProfile.getStudentId())
        .studentName(medicalProfile.getStudentName())
        .bloodGroup(medicalProfile.getBloodGroup())
        .height(medicalProfile.getHeight())
        .weight(medicalProfile.getWeight())
        .drugOrMedicines(medicalProfile.getDrugOrMedicines())
        .foodAllergy(medicalProfile.getFoodAllergy())
        .chronicDiseases(medicalProfile.getChronicDiseases())
        .heartCondition(medicalProfile.getHeartCondition())
        .surgeryOrAdmittedHospital(medicalProfile.getSurgeryOrAdmittedHospital())
        .wearsSpectacles(medicalProfile.getWearsSpectacles())
        .dentalTreatment(medicalProfile.getDentalTreatment())
        .allergies(medicalProfile.getAllergies())
        .illness(medicalProfile.getIllness())
        .remarks(medicalProfile.getRemarks())
        .guardianDetails(
            student.getGuardians().stream()
                .map(
                    guardian ->
                        MedicalProfileDto.GuardianData.builder()
                            .guardianName(guardian.getFirstName())
                            .phoneNumber(
                                StringUtils.isEmpty(guardian.getMobileNumber())
                                        || "null".equalsIgnoreCase(guardian.getMobileNumber())
                                    ? null
                                    : Long.valueOf(guardian.getMobileNumber()))
                            .relationType(guardian.getRelationType())
                            .build())
                .toList())
        .build();
  }

  public MedicalProfileDto.Request getStudentMedicalHistory(Long studentId) {

    var student = studentAuthService.validateStudentById(studentId);
    var medicalHistory = medicalRepository.findByStudentId(student.getId());

    if (medicalHistory.isEmpty()) {
      return MedicalProfileDto.Request.builder().build();
    }
    return MedicalProfileDto.Request.builder()
        .bloodGroup(medicalHistory.get().getBloodGroup())
        .height(medicalHistory.get().getHeight())
        .weight(medicalHistory.get().getWeight())
        .drugOrMedicines(medicalHistory.get().getDrugOrMedicines())
        .foodAllergy(medicalHistory.get().getFoodAllergy())
        .guardianDetails(
            student.getGuardians().stream()
                .map(
                    guardian ->
                        MedicalProfileDto.GuardianData.builder()
                            .guardianName(guardian.getFirstName())
                            .phoneNumber(
                                StringUtils.isEmpty(guardian.getMobileNumber())
                                        || "null".equalsIgnoreCase(guardian.getMobileNumber())
                                    ? null
                                    : Long.valueOf(guardian.getMobileNumber()))
                            .relationType(guardian.getRelationType())
                            .build())
                .toList())
        .allergies(medicalHistory.get().getAllergies())
        .illness(medicalHistory.get().getIllness())
        .chronicDiseases(medicalHistory.get().getChronicDiseases())
        .heartCondition(medicalHistory.get().getHeartCondition())
        .surgeryOrAdmittedHospital(medicalHistory.get().getSurgeryOrAdmittedHospital())
        .wearsSpectacles(medicalHistory.get().getWearsSpectacles())
        .dentalTreatment(medicalHistory.get().getDentalTreatment())
        .remarks(medicalHistory.get().getRemarks())
        .build();
  }
}
