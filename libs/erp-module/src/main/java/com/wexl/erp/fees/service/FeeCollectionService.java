package com.wexl.erp.fees.service;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.*;
import com.wexl.erp.fees.repository.*;
import com.wexl.erp.paymentGateway.dto.PaymentGatewayDto;
import com.wexl.erp.paymentGateway.dto.PaymentMethod;
import com.wexl.erp.paymentGateway.dto.PaymentStatus;
import com.wexl.erp.paymentGateway.model.PaymentGatewayDetail;
import com.wexl.erp.paymentGateway.repository.PaymentGatewayDetailRepository;
import com.wexl.erp.paymentGateway.types.PaymentGatewayEngine;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.util.ValidationUtils;
import java.io.InputStream;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeCollectionService {

  private final FeeHeadRepository feeHeadRepository;
  private final PaymentGatewayEngine paymentGatewayEngine;
  private final PaymentGatewayDetailRepository paymentGatewayDetailRepository;
  private final FeePaymentRepository feePaymentRepository;
  private final FeePaymentDetailsRepository feePaymentDetailsRepository;
  private final FeeHeadService feeHeadService;
  private final FeeGroupFeeTypeRepository feeGroupFeeTypeRepository;
  private final ValidationUtils validationUtils;
  private final FeeGroupRepository feeGroupRepository;
  private final ConcessionRepository concessionRepository;
  private final ConcessionHeadRepository concessionHeadRepository;
  private final AuthService authService;
  private final FeeService feeService;

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  public PaymentGatewayDto.InitiatePaymentResponse collectFee(
      String feeHeadId, String orgSlug, FeeDto.CollectFeeRequest request) {
    var feeHead = getFeeHeadById(feeHeadId, orgSlug);
    var config = getConfig(orgSlug, request.paymentMethod());
    validateBalanceAmount(feeHead, request);
    var response = paymentGatewayEngine.initiatePayment(orgSlug, request, feeHead, config);
    if (response.referenceId() == null || response.referenceId().isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Payment initiation failed");
    }
    var feePayment = updateFeePayment(feeHead, request, response, config, orgSlug);
    var feeGroupFeeType =
        feeGroupFeeTypeRepository.findByFeeGroupIdAndOrgSlug(
            feeHead.getFeeMaster().getFeeGroup().getId(), feeHead.getOrgSlug());
    var feeHeadFeeType =
        feeGroupFeeType.stream()
            .filter(x -> x.getFeeType().equals(feeHead.getFeeType()))
            .findFirst()
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "Fee type not found for fee head: " + feeHead.getId()));
    var fineAmount = feeHeadService.calculateFineAmount(feeHead, feeHeadFeeType);
    var totalAmount = feeHead.getBalanceAmount();
    if (feeHead.getPaidAmount() == null) {
      totalAmount = feeHead.getBalanceAmount() + fineAmount;
    }
    feeHead.setStatus(getFeeStatus(feeHead, request.amount()));
    feeHead.setPaidAmount(
        feeHead.getPaidAmount() != null
            ? feeHead.getPaidAmount() + request.amount()
            : request.amount());
    feeHead.setBalanceAmount(totalAmount - request.amount());
    feeHeadRepository.save(feeHead);
    return PaymentGatewayDto.InitiatePaymentResponse.builder()
        .paymentId(feePayment.getId().toString())
        .paymentStatus(feePayment.getPaymentStatus())
        .rzPayOrderId(
            request.paymentMethod().equals(PaymentMethod.CASH_PAYMENT)
                ? null
                : response.referenceId())
        .rzPayKey(config.getConfig() == null ? null : config.getConfig().getKeyId())
        .easeBuzzHash(response.hash())
        .message("Payment initiated successfully")
        .build();
  }

  private PaymentGatewayDetail getConfig(String orgSlug, PaymentMethod paymentMethod) {
    var config =
        paymentGatewayDetailRepository.findByOrgSlugAndPaymentMethod(orgSlug, paymentMethod);
    if (config == null) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR,
          "RazorPay configuration not found for organization: " + orgSlug);
    }
    return config;
  }

  private FeePayment updateFeePayment(
      FeeHead feeHead,
      FeeDto.CollectFeeRequest request,
      PaymentGatewayDto.Response response,
      PaymentGatewayDetail config,
      String orgSlug) {

    FeePayment feePayment = new FeePayment();
    feePayment.setOrgSlug(orgSlug);
    feePayment.setPaymentMethod(request.paymentMethod());
    feePayment.setPaymentGatewayDetail(config);
    feePayment.setReferenceId(response.referenceId());
    feePayment.setTxnId(response.txnId());
    feePayment.setStudent(feeHead.getStudent());
    feePayment.setTotalAmountPaid(request.amount());
    feePayment.setPaymentStatus(
        request.paymentMethod().equals(PaymentMethod.CASH_PAYMENT)
            ? PaymentStatus.SUCCESS
            : PaymentStatus.INITIATED);

    feePaymentRepository.save(feePayment);
    feePaymentDetailsRepository.save(
        FeePaymentDetail.builder()
            .feeHead(feeHead)
            .amountPaid(request.amount())
            .feePayment(feePayment)
            .build());

    return feePayment;
  }

  private void validateBalanceAmount(FeeHead feeHead, FeeDto.CollectFeeRequest request) {
    if (request.amount() <= 0) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Payment Amount must be greater than zero");
    }
    if (feeHead.getBalanceAmount() <= 0) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "No Pending Fees");
    }
  }

  private FeeStatus getFeeStatus(FeeHead feeHead, Double amount) {
    double balanceAmount = feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0;
    double fineAmount = feeHead.getFineAmount() != null ? feeHead.getFineAmount() : 0.0;
    double remaining = balanceAmount - amount;
    if (remaining <= 0) {
      return FeeStatus.PAID;
    } else if (amount > 0 && remaining > 0 && amount < (balanceAmount + fineAmount)) {
      return FeeStatus.PARTIALLY_PAID;
    } else {
      return FeeStatus.UNPAID;
    }
  }

  private FeeHead getFeeHeadById(String feeHeadId, String orgSlug) {
    return feeHeadRepository
        .findByIdAndOrgSlug(UUID.fromString(feeHeadId), orgSlug)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "Fee head not found"));
  }

  public PaymentGatewayDto.PaymentResponse verifyPayment(
      PaymentGatewayDto.VerifyPaymentRequest verifyPaymentRequest,
      String orgSlug,
      String paymentId) {
    try {
      var config = getConfig(orgSlug, verifyPaymentRequest.paymentMethod());
      paymentGatewayEngine.verifyPayment(orgSlug, paymentId, verifyPaymentRequest, config);
      var payment = getPaymentById(paymentId, orgSlug);
      validatePaymentByStatus(payment, PaymentStatus.INITIATED);
      return buildPaymentResponse(updatePayment(payment, verifyPaymentRequest));
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private FeePayment getPaymentById(String paymentId, String orgSlug) {
    return feePaymentRepository
        .findByIdAndOrgSlug(UUID.fromString(paymentId), orgSlug)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "Order not initiated for the given status"));
  }

  private FeePayment updatePayment(
      FeePayment feePayment, PaymentGatewayDto.VerifyPaymentRequest verifyPaymentRequest) {
    feePayment.setPaymentStatus(PaymentStatus.SUCCESS);
    feePayment.setRazorPayTransactionId(verifyPaymentRequest.razorpayOrderId());
    feePayment.setRazorpayPaymentId(verifyPaymentRequest.razorpayPaymentId());
    return feePaymentRepository.save(feePayment);
  }

  private PaymentGatewayDto.PaymentResponse buildPaymentResponse(FeePayment feePayment) {
    return PaymentGatewayDto.PaymentResponse.builder()
        .paymentId(feePayment.getId().toString())
        .status(feePayment.getPaymentStatus())
        .message("Payment verified successfully")
        .build();
  }

  private void validatePaymentByStatus(FeePayment payment, PaymentStatus status) {
    if (payment.getPaymentStatus() != status) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Payment not initiated for the given status: " + status);
    }
  }

  public void migrateFeeHeads(String orgSlug) {
    var feeHeads = feeHeadRepository.findAllByOrgSlug(orgSlug);
    if (feeHeads.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "No Fee Heads found for migration");
    }
    for (FeeHead feeHead : feeHeads) {
      var feePaymentDetails = feePaymentDetailsRepository.findByFeeHead(feeHead);
      if (feePaymentDetails != null && !feePaymentDetails.isEmpty()) {
        LocalDateTime latestCreatedAt =
            feePaymentDetails.stream()
                .map(FeePaymentDetail::getCreatedAt)
                .max(Comparator.naturalOrder())
                .orElse(null)
                .toLocalDateTime();
        var amount = feeHead.getAmount();
        var amountPaid = feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0;
        var paidAmount = amountPaid + feeHead.getDiscountAmount();

        if (latestCreatedAt != null
            && latestCreatedAt.isBefore(feeHead.getDueDate())
            && paidAmount == amount) {
          feeHead.setStatus(FeeStatus.PAID);
          feeHead.setBalanceAmount(0.0);
          feeHeadRepository.save(feeHead);
        }
      }
    }
  }

  public void importConcessions(String orgSlug, MultipartFile file) {
    try (InputStream inputStream = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(inputStream)) {

      Sheet sheet = workbook.getSheetAt(0);
      for (Row row : sheet) {
        if (row.getRowNum() == 0) continue;
        int rowNumber = row.getRowNum();
        log.info("Start executing row number {}", rowNumber);
        String admissionNumber = row.getCell(0).getStringCellValue();
        String studentName = row.getCell(1).getStringCellValue();
        String className = row.getCell(2).getStringCellValue();
        String feeGroupSlug = row.getCell(3).getStringCellValue();
        String feeTypeSlug = row.getCell(4).getStringCellValue();
        String concessionHead = row.getCell(5).getStringCellValue();
        double totalFee = row.getCell(6).getNumericCellValue();
        double concessionAmount = row.getCell(7).getNumericCellValue();
        double rawValue = row.getCell(8).getNumericCellValue();
        double percentage = Math.floor(rawValue * 100) / 100.0;

        if (percentage != 0) {
          var user = validationUtils.isValidUser(admissionNumber);
          var section = user.getStudentInfo().getSection();
          var feeGroup = getFeeGroup(feeGroupSlug, section);
          var feeGroupFeeType =
              feeGroupFeeTypeRepository
                  .findByFeeGroupIdAndOrgSlug(feeGroup.getId(), orgSlug)
                  .stream()
                  .filter(x -> x.getFeeType().getName().contains(feeTypeSlug))
                  .findFirst()
                  .orElseThrow(
                      () ->
                          new ApiException(
                              InternalErrorCodes.INVALID_REQUEST,
                              "Fee type not found for fee group: " + feeGroupSlug));
          var concessionData = getConcessionData(concessionHead, percentage);

          var feeHead =
              feeHeadRepository.findByStudentAndFeeType(
                  user.getStudentInfo(), feeGroupFeeType.getFeeType());
          if (feeHead.isEmpty()) {
            throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Fee Head not found");
          }
          applyConcession(
              feeHead.get(),
              concessionData,
              user.getStudentInfo(),
              feeGroupFeeType.getFeeType(),
              concessionAmount);
          log.info("End executing row number {}", rowNumber);
        }
      }

    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Failed to process Excel file", e);
    }
  }

  private void createFeeHeadForStudent(
      Long studentId,
      FeeMaster feeMaster,
      FeeType feeType,
      String orgSlug,
      FeeGroup feeGroup,
      Concession concession) {
    var student = validationUtils.isStudentValid(studentId);
    var feeHead =
        feeHeadService.saveFeeHead(student, feeMaster, feeType, orgSlug, feeGroup, concession);
    var concessionHead =
        ConcessionHead.builder()
            .feeType(feeType)
            .concession(concession)
            .createdBy(authService.getUserDetails())
            .student(student)
            .isApproved(Boolean.FALSE)
            .feeHead(feeHead)
            .build();
    concessionHeadRepository.save(concessionHead);
  }

  public void applyConcession(
      FeeHead feeHead,
      Concession concession,
      Student student,
      FeeType feeType,
      double concessionAmount) {
    var isConcessionHeadExists =
        concessionHeadRepository.findByConcessionAndStudentAndFeeType(concession, student, feeType);
    if (isConcessionHeadExists.isEmpty()) {

      var concessionHead =
          ConcessionHead.builder()
              .feeType(feeType)
              .concession(concession)
              .createdBy(authService.getUserDetails())
              .student(student)
              .isApproved(Boolean.FALSE)
              .feeHead(feeHead)
              .build();
      concessionHeadRepository.save(concessionHead);
      feeHead.setDiscountAmount(concessionAmount);
      feeHead.setConcession(concession);
      feeHeadRepository.save(feeHead);
    }
  }

  private Concession getConcessionData(String concessionName, double percentage) {
    return findByDescriptionAndValue(concessionName, percentage);
  }

  private Concession findByDescriptionAndValue(String description, double value) {
    var concession = concessionRepository.findByDescriptionAndValue(description, value);
    if (concession.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Concession not found for description: " + description);
    }
    return concession.get();
  }

  private FeeGroup getFeeGroup(String feeGroupSlug, Section grade) {
    if (feeGroupSlug.equals("Tuition Fee")) {
      return getTuitionFeeData(grade);
    } else if (feeGroupSlug.equals("Admission Fee")) {
      return feeGroupRepository.findByNameAndOrgSlug("Admission Fee", grade.getOrganization());
    }
    throw new ApiException(
        InternalErrorCodes.INVALID_REQUEST, "Invalid fee group slug: " + feeGroupSlug);
  }

  private FeeGroup getTuitionFeeData(Section section) {

    switch (section.getGradeSlug()) {
      case "pre-nur" -> {
        return feeGroupRepository.findByNameAndOrgSlug(
            "Pre-nursery Tution Fee", section.getOrganization());
      }
      case "nur" -> {
        return feeGroupRepository.findByNameAndOrgSlug(
            "Nursery Tution Fee", section.getOrganization());
      }
      case "lkg", "ukg" -> {
        return feeGroupRepository.findByNameAndOrgSlug(
            "LKG & UKG – Tuition Fee (Term-wise)", section.getOrganization());
      }
      case "i", "ii", "iii", "iv", "v" -> {
        return feeGroupRepository.findByNameAndOrgSlug(
            "Grade 1 to 5 – Tuition Fee (Term-wise)", section.getOrganization());
      }
      case "vi", "vii", "viii" -> {
        return feeGroupRepository.findByNameAndOrgSlug(
            "Grade 6 to 8 – Tuition Fee (Term-wise)", section.getOrganization());
      }
      case "ix", "x" -> {
        return feeGroupRepository.findByNameAndOrgSlug(
            "Grade 9 to 10 – Tuition Fee (Term-wise)", section.getOrganization());
      }
      case "xig", "xiig" -> {
        return feeGroupRepository.findByNameAndOrgSlug(
            "Grade 11-Science to 12-Science – Tuition Fee (Term-wise)", section.getOrganization());
      }
      default ->
          throw new ApiException(
              InternalErrorCodes.INVALID_REQUEST, "Invalid grade: " + section.getGradeSlug());
    }
  }

  public void updateTransactionStatus(String txnId, PaymentStatus paymentStatus) {
    FeePayment feePayment = feePaymentRepository.findByTxnId(txnId).orElseThrow();
    feePayment.setPaymentStatus(paymentStatus);
    feePaymentRepository.save(feePayment);
  }

  public Map<String, Object> getFeeDueAlert(String orgSlug, String studentId) {
    var student = validationUtils.validateStudentByAuthId(studentId, orgSlug);
    LocalDate date = LocalDate.now();
    var feeHeads =
        feeHeadRepository.getByOrgSlugAndStudentIdAndDueDate(orgSlug, student.getId(), date);

    double termFee = 0.0;
    double transportFee = 0.0;
    LocalDate latestTermDate = null;
    Set<String> termNames = new HashSet<>();

    for (var fh : feeHeads) {
      String type = fh.getTypeName();

      if (type != null && type.toUpperCase().startsWith("TERM")) {
        termFee += fh.getDue();
        termNames.add(type);
        LocalDate dueDate = fh.getDueDate();
        if (latestTermDate == null || dueDate.isAfter(latestTermDate)) {
          latestTermDate = dueDate;
        }
      } else {
        transportFee += fh.getDue();
      }
    }
    Map<String, Object> result = new HashMap<>();
    result.put("name", String.join(", ", termNames));
    result.put("term_fee", termFee);
    result.put("transport_fee", transportFee);
    result.put("due_date", latestTermDate);
    if (latestTermDate != null) {
      long daysDiff = ChronoUnit.DAYS.between(latestTermDate, date);
      if (daysDiff > 0) {
        result.put("status", "due delay by " + daysDiff + " days");
      } else if (Math.abs(daysDiff) <= 11) {
        result.put("status", "due by " + Math.abs(daysDiff) + " days");
      }
    }
    return result;
  }

  public List<PaymentGatewayDto.PaymentMethodResponse> getAllPaymentTypes(String orgSlug) {

    var paymentGatewayDetails = paymentGatewayDetailRepository.findByOrgSlug(orgSlug);

    if (paymentGatewayDetails.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "No Payment Methods found for organization: " + orgSlug);
    }
    return paymentGatewayDetails.stream()
        .filter(
            paymentGatewayDetail ->
                !"CASH_PAYMENT"
                    .equalsIgnoreCase(String.valueOf(paymentGatewayDetail.getPaymentMethod())))
        .map(
            paymentGatewayDetail ->
                PaymentGatewayDto.PaymentMethodResponse.builder()
                    .paymentMethod(paymentGatewayDetail.getPaymentMethod())
                    .orgSlug(orgSlug)
                    .build())
        .toList();
  }

  public void importAssignFeeHeads(String orgSlug, MultipartFile file) {
    try (InputStream inputStream = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(inputStream)) {

      Sheet sheet = workbook.getSheetAt(0);

      Map<String, List<String>> groupedStudents = new HashMap<>();
      DataFormatter formatter = new DataFormatter();

      for (Row row : sheet) {
        if (row.getRowNum() == 0) continue;
        int rowNumber = row.getRowNum();
        log.info("Start executing row number {}", rowNumber);

        String sno = formatter.formatCellValue(row.getCell(0));
        String studentAuthId = formatter.formatCellValue(row.getCell(1));
        String studentName = formatter.formatCellValue(row.getCell(2));
        double annualFee = row.getCell(7) != null ? row.getCell(7).getNumericCellValue() : 0;
        String feeGroupSlug = formatter.formatCellValue(row.getCell(10));

        if (annualFee == 0 || feeGroupSlug.isEmpty()) {
          log.warn("Skipping row {} due to missing annual fee or fee group slug", rowNumber);
          continue;
        }
        String key = feeGroupSlug;

        groupedStudents.computeIfAbsent(key, k -> new ArrayList<>()).add(studentAuthId);
      }

      groupedStudents.forEach(
          (key, students) -> {
            List<User> users = students.stream().map(validationUtils::isValidUser).toList();
            List<Long> studentIds =
                users.stream().map(User::getStudentInfo).map(Student::getId).toList();
            var section = users.getFirst().getStudentInfo().getSection();
            feeService.saveFeeMaster(
                orgSlug,
                FeeDto.FeeMasterRequest.builder()
                    .feeGroupId(key)
                    .scopeType(ScopeType.CUSTOM)
                    .boardSlug(section.getBoardSlug())
                    .academicYear(latestAcademicYear)
                    .gradeSlug(Collections.singletonList(section.getGradeSlug()))
                    .sectionUuid(Collections.singletonList(section.getUuid().toString()))
                    .studentId(studentIds)
                    .build());

            log.info("Group {} has students: {}", key, students);
          });
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Failed to process Excel file", e);
    }
  }

  public void importFeePayments(String orgSlug, MultipartFile file) {
    try (InputStream inputStream = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(inputStream)) {

      Sheet sheet = workbook.getSheetAt(0);

      DataFormatter formatter = new DataFormatter();

      for (Row row : sheet) {
        if (row.getRowNum() == 0) continue;
        int rowNumber = row.getRowNum();

        String sno = formatter.formatCellValue(row.getCell(0));
        String transactionDate = formatter.formatCellValue(row.getCell(3));
        String studentAuthId = formatter.formatCellValue(row.getCell(4));
        String studentName = formatter.formatCellValue(row.getCell(5));
        String feeGroup = formatter.formatCellValue(row.getCell(12));
        String feeType = formatter.formatCellValue(row.getCell(13));
        double amount = row.getCell(14) != null ? row.getCell(14).getNumericCellValue() : 0;
        String paymentMode = formatter.formatCellValue(row.getCell(15));
        String collectedBy = formatter.formatCellValue(row.getCell(16));
        String referenceNumber = formatter.formatCellValue(row.getCell(17));
        int totalAmount = row.getCell(18) != null ? (int) row.getCell(18).getNumericCellValue() : 0;
        log.info("Start executing row number {},{}", rowNumber, studentName);

        if (amount == 0 || feeGroup.isEmpty() || feeType.isEmpty()) {
          log.warn("Skipping row {} due to missing  feeGroup or fee feeType", rowNumber);
          continue;
        }
        var user = validationUtils.isValidUser(studentAuthId);
        var student = user.getStudentInfo();
        var feeGroupData = getFeeHeadForStudent(student, feeGroup, totalAmount);
        if (feeGroupData == null) {
          continue;
        }
        var feeGroupFeeType =
            feeGroupFeeTypeRepository
                .findByFeeGroupIdAndOrgSlug(feeGroupData.getId(), orgSlug)
                .stream()
                .filter(x -> x.getFeeType().getName().equalsIgnoreCase(feeType))
                .findFirst()
                .orElseThrow(
                    () ->
                        new ApiException(
                            InternalErrorCodes.INVALID_REQUEST,
                            "Fee type not found for fee group: " + feeGroupData));
        var feeHead =
            feeHeadRepository.getFeeHeadByStudentIdAnFeeMasterIdAndFeeType(
                feeGroupData.getId(),
                orgSlug,
                user.getStudentInfo().getId(),
                feeGroupFeeType.getFeeType().getId());
        if (feeHead.isEmpty()) {
          throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Fee Head not found");
        }
        updateFeePaymentDetails(
            feeHead.get(), amount, paymentMode, orgSlug, referenceNumber, transactionDate);
        log.info("completed executing row number {},{}", rowNumber, studentName);
      }

    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Failed to process Excel file", e);
    }
  }

  private void updateFeePaymentDetails(
      FeeHead feeHead,
      double amount,
      String paymentMode,
      String orgSlug,
      String referenceNumber,
      String transactionDate) {

    PaymentMethod paymentType =
        "cash".equalsIgnoreCase(paymentMode)
            ? PaymentMethod.CASH_PAYMENT
            : PaymentMethod.RAZOR_PAYMENT;

    var config = getConfig(orgSlug, paymentType);

    FeePayment feePayment = new FeePayment();
    feePayment.setCreatedAt(Timestamp.valueOf(transactionDate));
    feePayment.setUpdatedAt(Timestamp.valueOf(transactionDate));
    feePayment.setOrgSlug(orgSlug);
    feePayment.setPaymentMethod(paymentType);
    feePayment.setPaymentGatewayDetail(config);
    feePayment.setReferenceId(referenceNumber);
    feePayment.setStudent(feeHead.getStudent());
    feePayment.setTotalAmountPaid(amount);
    feePayment.setPaymentStatus(
        paymentType.equals(PaymentMethod.CASH_PAYMENT)
            ? PaymentStatus.SUCCESS
            : PaymentStatus.INITIATED);

    feePaymentRepository.save(feePayment);
    FeePaymentDetail feePaymentDetail = new FeePaymentDetail();
    feePaymentDetail.setFeeHead(feeHead);
    feePaymentDetail.setAmountPaid(amount);
    feePaymentDetail.setFeePayment(feePayment);
    feePaymentDetail.setCreatedAt(Timestamp.valueOf(transactionDate));
    feePaymentDetail.setUpdatedAt(Timestamp.valueOf(transactionDate));
    feePaymentDetailsRepository.save(feePaymentDetail);

    double currentPaidAmount = feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0;
    feeHead.setPaidAmount(currentPaidAmount + amount);
    feeHead.setBalanceAmount(feeHead.getBalanceAmount() - amount);

    feeHeadRepository.save(feeHead);
  }

  private FeeGroup getFeeHeadForStudent(Student student, String feeGroup, int totalAmount) {
    var section = student.getSection();
    String feeHead = feeGroup.trim();

    return switch (feeHead.toLowerCase()) {
      case "tuition fee" -> {
        String feeGroupName = getTuitionGroup(section);
        yield feeGroupRepository.findByNameAndOrgSlug(feeGroupName, section.getOrganization());
      }
      case "admission fee", "annual fee" -> {
        String feeGroupName = feeHead + "(" + totalAmount + ")";
        yield feeGroupRepository.findByNameAndOrgSlug(feeGroupName, section.getOrganization());
      }
      default -> null;
    };
  }

  private String getTuitionGroup(Section section) {
    String gradeSlug = section.getGradeSlug().toLowerCase();
    String sectionName = section.getName().toLowerCase();

    if (SPECIAL_GRADE_MAP.containsKey(gradeSlug)) {
      return SPECIAL_GRADE_MAP.get(gradeSlug);
    }
    if (gradeSlug.equals("xig") || (gradeSlug.equals("xiig") && sectionName.contains("arts"))) {
      return "Tuition Fee (Grade 11 & 12 Arts)";
    }
    if (gradeSlug.equals("xiig")) {
      return "Tuition Fee (Grade 12 Science)";
    }

    String grade = ROMAN_TO_GRADE_MAP.get(gradeSlug);
    if (grade != null) {
      return "Tuition Fee (Grade " + grade + ")";
    }

    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid grade: " + gradeSlug);
  }

  private static final Map<String, String> SPECIAL_GRADE_MAP =
      Map.of(
          "pre-nur", "Tuition Fee - Nursery",
          "nur", "Tuition Fee - Nursery",
          "lkg", "Tuition Fee - LKG",
          "ukg", "Tuition Fee - UKG");
  private static final Map<String, String> ROMAN_TO_GRADE_MAP =
      Map.of(
          "i", "1", "ii", "2", "iii", "3", "iv", "4", "v", "5", "vi", "6", "vii", "7", "viii", "8",
          "ix", "9", "x", "10");
}
