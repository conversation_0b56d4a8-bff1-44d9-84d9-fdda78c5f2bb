package com.wexl.erp.fees.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.erp.fees.model.ConcessionType;
import com.wexl.erp.fees.model.FeeStatus;
import com.wexl.erp.fees.model.FineType;
import com.wexl.erp.fees.model.ScopeType;
import com.wexl.erp.paymentGateway.dto.PaymentMethod;
import java.util.List;
import java.util.UUID;
import lombok.Builder;

public record FeeDto() {

  public record FeeTypeRequest(String name, String code, String description) {}

  @Builder
  public record FeeTypeResponse(
      UUID id,
      String name,
      String code,
      String description,
      @JsonProperty("created_by") String createdBy,
      @JsonProperty("created_by_user_id") Long userId,
      @JsonProperty("created_on") Long createdOn) {}

  public record FeeGroupRequest(String name, String description) {}

  @Builder
  public record FeeGroupResponse(
      UUID id,
      String name,
      @JsonProperty("is_active") Boolean isActive,
      String description,
      @JsonProperty("created_by") String createdBy,
      @JsonProperty("created_by_user_id") Long userId,
      @JsonProperty("published_at") Long publishedAt,
      @JsonProperty("fee_master_exists") Boolean feeMasterExists,
      @JsonProperty("created_on") Long createdOn) {}

  public record FeeGroupFeeTypeRequest(@JsonProperty("fee_types") List<FeeTypes> feeTypes) {}

  public record FeeTypes(
      FineType fineType,
      @JsonProperty("fee_type_id") String feeTypeId,
      @JsonProperty("due_date") Long dueDate,
      @JsonProperty("cumulative_fine") List<CumulativeFine> cumulativeFine,
      Long percentage,
      Double amount,
      @JsonProperty("fine_amount") Double fineAmount,
      @JsonProperty("remainder_days") Long remainderDays) {}

  @Builder
  public record CumulativeFine(
      @JsonProperty("due_date") Long dueDate, @JsonProperty("fine_amount") Double fineAmount) {}

  @Builder
  public record FeeGroupFeeTypeResponse(
      @JsonProperty("fee_group_id") String feeGroupId,
      String name,
      String description,
      List<FeeTypesResponse> feeTypes) {}

  @Builder
  public record FeeTypesResponse(
      String id,
      FineType fineType,
      @JsonProperty("created_by") String createdBy,
      @JsonProperty("created_by_user_id") Long userId,
      @JsonProperty("created_on") Long createdOn,
      @JsonProperty("fee_type_id") String feeTypeId,
      @JsonProperty("due_date") Long dueDate,
      Double amount,
      String name,
      Long percentage,
      @JsonProperty("fine_amount") Double fineAmount,
      @JsonProperty("cumulative_fine") List<CumulativeFineResponse> cumulativeFine,
      @JsonProperty("remainder_days") Long remainderDays) {}

  @Builder
  public record CumulativeFineResponse(
      String id,
      @JsonProperty("due_date") Long dueDate,
      @JsonProperty("fine_amount") Double fineAmount) {}

  public record FeeGroupFeeTypeUpdateRequest(
      FineType fineType,
      @JsonProperty("fee_type_id") String feeTypeId,
      @JsonProperty("due_date") Long dueDate,
      Double amount,
      @JsonProperty("fine_amount") Double fineAmount,
      @JsonProperty("remainder_days") Long remainderDays) {}

  @Builder
  public record FeeMasterRequest(
      @JsonProperty("academic_year") String academicYear,
      @JsonProperty("fee_group_id") String feeGroupId,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("scope_type") ScopeType scopeType,
      @JsonProperty("grade_slug") List<String> gradeSlug,
      @JsonProperty("section_uuid") List<String> sectionUuid,
      @JsonProperty("student_id") List<Long> studentId) {}

  @Builder
  public record FeeMasterResponse(
      @JsonProperty("id") String id,
      @JsonProperty("academic_year") String academicYear,
      @JsonProperty("fee_group_id") String feeGroupId,
      @JsonProperty("fee_group_description") String feeGroupDescription,
      @JsonProperty("fee_group_name") String feeGroupName,
      @JsonProperty("scope_type") ScopeType scopeType,
      @JsonProperty("created_by_user_id") Long userId,
      @JsonProperty("created_on") Long createdOn) {}

  @Builder
  public record FeeHeadResponse(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_uuid") String sectionUuid,
      @JsonProperty("section_name") String sectionName) {}

  @Builder
  public record Rules(
      @JsonProperty("param_type") String paramType,
      @JsonProperty("param_value") List<String> paramValue) {}

  @Builder
  public record StudentsFeeHeadResponse(
      @JsonProperty("fee_head_id") String feeHeadId,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("grade_slug") String gradeSlug,
      FeeStatus status,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_uuid") String sectionUuid,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("fee_master_id") UUID feeMasterId,
      Double amount,
      @JsonProperty("fine_amount") Double fineAmount,
      @JsonProperty("fine_type") FineType fineType,
      @JsonProperty("discount_amount") Double discountAmount,
      @JsonProperty("paid_amount") Double paidAmount,
      @JsonProperty("balance_amount") Double balanceAmount,
      @JsonProperty("due_date") Long dueDate,
      @JsonProperty("fee_type_id") UUID feeTypeId,
      @JsonProperty("fee_type_code") String feeTypeCode,
      @JsonProperty("fee_type_name") String feeTypeName,
      @JsonProperty("fee_type_description") String feeTypeDescription,
      @JsonProperty("fee_group_id") String feeGroupId,
      @JsonProperty("fee_group_name") String feeGroupName,
      @JsonProperty("created_on") Long createdOn,
      @JsonProperty("quarter") String quarter,
      @JsonProperty("paid_date") Long paidDate) {}

  @Builder
  public record StudentsFeeHeadResponseData(
      @JsonProperty("q2_april_to_june") List<StudentsFeeHeadResponse> q2,
      @JsonProperty("q3_july_to_september") List<StudentsFeeHeadResponse> q3,
      @JsonProperty("q4_october_to_december") List<StudentsFeeHeadResponse> q4,
      @JsonProperty("q1_january_to_march") List<StudentsFeeHeadResponse> q1) {}

  public record ConcessionRequest(
      @JsonProperty("concession_type") ConcessionType concessionType,
      Double value,
      String description) {}

  @Builder
  public record ConcessionResponse(
      UUID id,
      ConcessionType concessionType,
      @JsonProperty("created_on") Long createdOn,
      @JsonProperty("created_by") String createdBy,
      @JsonProperty("created_by_user_id") Long userId,
      Double value,
      @JsonProperty("is_published") Boolean isPublished,
      String description) {}

  public record ConcessionHeadRequest(
      @JsonProperty("fee_head_id") String feeHeadId,
      @JsonProperty("student_id") List<Long> studentId,
      @JsonProperty("concession_id") List<String> ConcessionId) {}

  @Builder
  public record ConcessionHeadResponse(
      @JsonProperty("fee_head_id") String feeHeadId,
      @JsonProperty("fee_type_code") String feeTypeCode,
      @JsonProperty("fee_type_name") String feeTypeName,
      @JsonProperty("fee_type_description") String feeTypeDescription,
      @JsonProperty("fee_master_id") String feeMasterId,
      @JsonProperty("fee_group_id") String feeGroupId,
      @JsonProperty("fee_group_description") String feeGroupDescription,
      @JsonProperty("fee_group_name") String feeGroupName,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName) {}

  @Builder
  public record CollectFeeRequest(
      Double amount, @JsonProperty("payment_method") PaymentMethod paymentMethod) {}

  public record AssignConcessionRequest(
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("fee_master_id") String feeMasterId,
      @JsonProperty("fee_group_id") String feeGroupId,
      @JsonProperty("fee_type_id") String feeTypeId,
      @JsonProperty("students") List<Long> students) {}

  public record RetireFeeHeadsRequest(@JsonProperty("fee_heads") List<String> feeHeads) {}

  @Builder
  public record FeeDueReportRequest(
      @JsonProperty("from_date") Long fromDate,
      @JsonProperty("to_date") Long toDate,
      @JsonProperty("report_type") String reportType,
      @JsonProperty("fee_group_types") List<String> feeGroupTypes,
      @JsonProperty("section_uuids") List<String> sectionUuids) {}

//  @Builder
//  public record FeeDueReportResponse(
//      @JsonProperty("student_name") String studentName,
//      @JsonProperty("admission_number") String admissionNumber,
//      @JsonProperty("roll_number") String rollNumber,
//      @JsonProperty("section_name") String sectionName,
//      @JsonProperty("date_of_admission") String dateOfAdmission,
//      @JsonProperty("fee_details") List<FeeDetailResponse> feeDetails,
//      @JsonProperty("discount_amount") Double discountAmount,
//      @JsonProperty("total_due_amount") Double totalDueAmount) {}

//  @Builder
//  public record FeeDetailResponse(
//      @JsonProperty("fee_type_name") String feeTypeName,
//      @JsonProperty("month") String month,
//      @JsonProperty("amount") Double amount,
//      @JsonProperty("paid_amount") Double paidAmount,
//      @JsonProperty("balance_amount") Double balanceAmount,
//      @JsonProperty("due_date") Long dueDate,
//      @JsonProperty("status") FeeStatus status) {}

  @Builder
  public record TotalDueReportResponse(
      @JsonProperty("student_name") String studentName,
      @JsonProperty("admission_number") String admissionNumber,
      @JsonProperty("roll_number") String rollNumber,
      @JsonProperty("class_name") String className,
      @JsonProperty("student_status") String studentStatus,
      @JsonProperty("father_name") String fatherName,
      @JsonProperty("father_mobile") String fatherMobile,
      @JsonProperty("father_email") String fatherEmail,
      @JsonProperty("mother_name") String motherName,
      @JsonProperty("mother_mobile") String motherMobile,
      @JsonProperty("mother_email") String motherEmail,
      @JsonProperty("dnd_status") String dndStatus,
      @JsonProperty("date_of_admission") String dateOfAdmission,
      @JsonProperty("student_category") String studentCategory,
      @JsonProperty("hostel_category") String hostelCategory,
      @JsonProperty("admission_fee") Double admissionFee,
      @JsonProperty("tuition_admission_fee") Double tuitionAdmissionFee,
      @JsonProperty("tuition_term1") Double tuitionTerm1,
      @JsonProperty("tuition_term2") Double tuitionTerm2,
      @JsonProperty("tuition_term3") Double tuitionTerm3,
      @JsonProperty("tuition_term4") Double tuitionTerm4,
      @JsonProperty("tuition_apr") Double tuitionApr,
      @JsonProperty("tuition_may") Double tuitionMay,
      @JsonProperty("tuition_jun") Double tuitionJun,
      @JsonProperty("tuition_jul") Double tuitionJul,
      @JsonProperty("tuition_aug") Double tuitionAug,
      @JsonProperty("tuition_sep") Double tuitionSep,
      @JsonProperty("tuition_oct") Double tuitionOct,
      @JsonProperty("tuition_nov") Double tuitionNov,
      @JsonProperty("tuition_dec") Double tuitionDec,
      @JsonProperty("tuition_jan") Double tuitionJan,
      @JsonProperty("tuition_feb") Double tuitionFeb,
      @JsonProperty("tuition_mar") Double tuitionMar,
      @JsonProperty("transport_apr") Double transportApr,
      @JsonProperty("transport_may") Double transportMay,
      @JsonProperty("transport_jun") Double transportJun,
      @JsonProperty("transport_jul") Double transportJul,
      @JsonProperty("transport_aug") Double transportAug,
      @JsonProperty("transport_sep") Double transportSep,
      @JsonProperty("transport_oct") Double transportOct,
      @JsonProperty("transport_nov") Double transportNov,
      @JsonProperty("transport_dec") Double transportDec,
      @JsonProperty("transport_jan") Double transportJan,
      @JsonProperty("transport_feb") Double transportFeb,
      @JsonProperty("transport_mar") Double transportMar,
      @JsonProperty("tuition_late_fee_term1") Double tuitionLateFeTerm1,
      @JsonProperty("tuition_late_fee_term2") Double tuitionLateFeTerm2,
      @JsonProperty("tuition_late_fee_term3") Double tuitionLateFeTerm3,
      @JsonProperty("tuition_late_fee_term4") Double tuitionLateFeTerm4,
      @JsonProperty("tuition_late_fee_apr") Double tuitionLateFeeApr,
      @JsonProperty("tuition_late_fee_may") Double tuitionLateFeeMay,
      @JsonProperty("tuition_late_fee_jun") Double tuitionLateFeeJun,
      @JsonProperty("tuition_late_fee_jul") Double tuitionLateFeeJul,
      @JsonProperty("tuition_late_fee_aug") Double tuitionLateFeeAug,
      @JsonProperty("tuition_late_fee_sep") Double tuitionLateFeeSep,
      @JsonProperty("tuition_late_fee_oct") Double tuitionLateFeeOct,
      @JsonProperty("tuition_late_fee_nov") Double tuitionLateFeeNov,
      @JsonProperty("tuition_late_fee_dec") Double tuitionLateFeeDec,
      @JsonProperty("tuition_late_fee_jan") Double tuitionLateFeeJan,
      @JsonProperty("tuition_late_fee_feb") Double tuitionLateFeeFeb,
      @JsonProperty("tuition_late_fee_mar") Double tuitionLateFeeMar,
      @JsonProperty("transport_late_fee_apr") Double transportLateFeeApr,
      @JsonProperty("transport_late_fee_may") Double transportLateFeeMay,
      @JsonProperty("transport_late_fee_jun") Double transportLateFeeJun,
      @JsonProperty("transport_late_fee_jul") Double transportLateFeeJul,
      @JsonProperty("transport_late_fee_aug") Double transportLateFeeAug,
      @JsonProperty("transport_late_fee_sep") Double transportLateFeeSep,
      @JsonProperty("transport_late_fee_oct") Double transportLateFeeOct,
      @JsonProperty("transport_late_fee_nov") Double transportLateFeeNov,
      @JsonProperty("transport_late_fee_dec") Double transportLateFeeDec,
      @JsonProperty("transport_late_fee_jan") Double transportLateFeeJan,
      @JsonProperty("transport_late_fee_feb") Double transportLateFeeFeb,
      @JsonProperty("transport_late_fee_mar") Double transportLateFeeMar,
      @JsonProperty("delete") String delete,
      @JsonProperty("extended_day_care_late_fee") Double extendedDayCareLateFeee,
      @JsonProperty("extended_day_care") Double extendedDayCare,
      @JsonProperty("total_fee_assigned") Double totalFeeAssigned,
      @JsonProperty("concession_amount") Double concessionAmount,
      @JsonProperty("total_paid") Double totalPaid,
      @JsonProperty("total_due") Double totalDue,
      @JsonProperty("fee_remark") String feeRemark) {}

  @Builder
  public record PastDueReportResponse(
      @JsonProperty("student_name") String studentName,
      @JsonProperty("admission_number") String admissionNumber,
      @JsonProperty("roll_number") String rollNumber,
      @JsonProperty("class_name") String className,
      @JsonProperty("student_status") String studentStatus,
      @JsonProperty("father_name") String fatherName,
      @JsonProperty("father_mobile") String fatherMobile,
      @JsonProperty("father_email") String fatherEmail,
      @JsonProperty("mother_name") String motherName,
      @JsonProperty("mother_mobile") String motherMobile,
      @JsonProperty("mother_email") String motherEmail,
      @JsonProperty("dnd_status") String dndStatus,
      @JsonProperty("date_of_admission") String dateOfAdmission,
      @JsonProperty("student_category") String studentCategory,
      @JsonProperty("hostel_category") String hostelCategory,
      @JsonProperty("admission_fee") Double admissionFee,
      @JsonProperty("tuition_admission_fee") Double tuitionAdmissionFee,
      @JsonProperty("tuition_term1") Double tuitionTerm1,
      @JsonProperty("tuition_term2") Double tuitionTerm2,
      @JsonProperty("transport_apr") Double transportApr,
      @JsonProperty("transport_may") Double transportMay,
      @JsonProperty("transport_jun") Double transportJun,
      @JsonProperty("transport_jul") Double transportJul,
      @JsonProperty("transport_aug") Double transportAug,
      @JsonProperty("extended_day_care") Double extendedDayCare,
      @JsonProperty("delete") String delete,
      @JsonProperty("total_fee_assigned_till_date") Double totalFeeAssignedTillDate,
      @JsonProperty("concession_amount") Double concessionAmount,
      @JsonProperty("total_paid_till_date") Double totalPaidTillDate,
      @JsonProperty("total_due") Double totalDue,
      @JsonProperty("fee_remark") String feeRemark) {}

  @Builder
  public record FeeDueReportResponse(
      @JsonProperty("total_due_report") List<TotalDueReportResponse> totalDueReport,
      @JsonProperty("past_due_report") List<PastDueReportResponse> pastDueReport) {}
}
