package com.wexl.erp.fees.controller;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.service.FeeCollectionService;
import com.wexl.erp.fees.service.FeeHeadService;
import com.wexl.erp.paymentGateway.dto.PaymentGatewayDto;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class FeeCollectionController {
  private final FeeCollectionService feeCollectionService;
  private final FeeHeadService feeHeadService;

  @PostMapping("/fee-heads/{feeHeadId}/fee-collection")
  public PaymentGatewayDto.InitiatePaymentResponse collectFee(
      @PathVariable String orgSlug,
      @PathVariable String feeHeadId,
      @RequestBody FeeDto.CollectFeeRequest request) {
    return feeCollectionService.collectFee(feeHeadId, orgSlug, request);
  }

  @PostMapping("/fee-heads/{feeHeadId}/fee-payments/{paymentId}:verify")
  public PaymentGatewayDto.PaymentResponse verifyPayment(
      @PathVariable String orgSlug,
      @PathVariable String paymentId,
      @Valid @RequestBody PaymentGatewayDto.VerifyPaymentRequest verifyPaymentRequest) {
    return feeCollectionService.verifyPayment(verifyPaymentRequest, orgSlug, paymentId);
  }

  @DeleteMapping("/fee-heads/{feeHeadId}/void-fee-head")
  public void voidFeeHead(@PathVariable String orgSlug, @PathVariable String feeHeadId) {
    feeHeadService.voidFeeHead(orgSlug, feeHeadId);
  }

  @PostMapping("/migrate-fee-heads")
  public void migrateFeeHeads(@PathVariable String orgSlug) {
    feeCollectionService.migrateFeeHeads(orgSlug);
  }

  @PostMapping("/import-concession-heads")
  public void importConcessions(
      @PathVariable String orgSlug, @RequestParam("file") MultipartFile file) {
    feeCollectionService.importConcessions(orgSlug, file);
  }

  @GetMapping("/students/{studentId}/fee:due")
  public Map<String, Object> getPaymentDetails(
      @PathVariable String orgSlug, @PathVariable String studentId) {
    return feeCollectionService.getFeeDueAlert(orgSlug, studentId);
  }

  @GetMapping("/payment-methods")
  public List<PaymentGatewayDto.PaymentMethodResponse> getAllPaymentMethods(
      @PathVariable String orgSlug) {
    return feeCollectionService.getAllPaymentTypes(orgSlug);
  }

  @PostMapping("/import-assign-fee-heads")
  public void importAssignFeeHeads(
      @PathVariable String orgSlug, @RequestParam("file") MultipartFile file) {
    feeCollectionService.importAssignFeeHeads(orgSlug, file);
  }

  @PostMapping("/import-fee-payments")
  public void importFeePayments(
      @PathVariable String orgSlug, @RequestParam("file") MultipartFile file) {
    feeCollectionService.importFeePayments(orgSlug, file);
  }
}
