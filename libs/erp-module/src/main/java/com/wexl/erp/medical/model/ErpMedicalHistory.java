package com.wexl.erp.medical.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wexl.erp.medical.dto.Allergies;
import com.wexl.erp.medical.dto.Illness;
import com.wexl.erp.medical.dto.Remarks;
import com.wexl.retail.guardian.model.Guardian;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "medical_histories")
public class ErpMedicalHistory {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "student_id")
  private Long studentId;

  @Column(name = "student_name")
  private String studentName;

  @Column(name = "blood_group")
  private String bloodGroup;

  @Column(name = "height_cm")
  private String height;

  @Column(name = "weight_kg")
  private String weight;

  @Column(name = "drug_or_medicines")
  private String drugOrMedicines;

  @Column(name = "food_allergy")
  private String foodAllergy;

  @Column(name = "chronic_diseases")
  private String chronicDiseases;

  @Column(name = "heart_condition")
  private String heartCondition;

  @Column(name = "surgery_or_admitted_hospital")
  private String surgeryOrAdmittedHospital;

  @Column(name = "wears_spectacles")
  private String wearsSpectacles;

  @Column(name = "dental_treatment")
  private String dentalTreatment;

  @Column(name = "org_slug")
  private String orgSlug;

  @Type(JsonType.class)
  @JsonIgnore
  @Column(name = "allergies", columnDefinition = "jsonb")
  private Allergies allergies;

  @Type(JsonType.class)
  @JsonIgnore
  @Column(name = "illness", columnDefinition = "jsonb")
  private Illness illness;

  @Type(JsonType.class)
  @JsonIgnore
  @Column(name = "remarks", columnDefinition = "jsonb")
  private Remarks remarks;

  @OneToMany(fetch = FetchType.LAZY)
  @JoinColumn(name = "guardian_id")
  private List<Guardian> guardians;
}
