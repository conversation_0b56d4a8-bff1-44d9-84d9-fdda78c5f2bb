package com.wexl.dps.reportcard;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.dps.dto.Scholar11th12thReportDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.holisticreportcards.repository.ProgressCardRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.ResourceUtils;
import com.wexl.retail.erp.attendance.dto.MedicalRecords;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceDetailRepository;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.InterOverAllReportDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.DoubleStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class Scholar11th12thReportCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final ProgressCardRepository progressCardRepository;
  private final LowerGradeFirstTermReportCard lowerGradeFirstTermReportCard;
  private final SectionAttendanceDetailRepository sectionAttendanceDetailRepository;

  @Value("classpath:scholars11th12th-co-scholastic.json")
  private Resource resource;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildReportCardHeader(user, org);
    var body = buildBody(user, org.getSlug(), request);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  private Scholar11th12thReportDto.Body buildBody(
      User user, String orgSlug, ReportCardDto.Request request) {
    var student = user.getStudentInfo();
    var data = getData(student);
    var guardians = student.getGuardians();
    var gradeSlug = student.getSection().getGradeSlug();
    var scholasticMandatory = buildScholasticMandatory(data);
    var getMarksResponse = getMarksResponse(scholasticMandatory);
    var mother =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findFirst();
    var father =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findFirst();
    var motherName = mother == null ? null : reportCardService.getGuardianName(mother.get());
    var fatherName = father == null ? null : reportCardService.getGuardianName(father.get());
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var remarks = lowerGradeFirstTermReportCard.buildAttendance(student.getId()).remarks();

    var studentData = sectionAttendanceDetailRepository.getMedicalRecords(student.getId(), orgSlug);

    return Scholar11th12thReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .rollNo(user.getStudentInfo().getRollNumber())
        .admissionNumber(user.getStudentInfo().getRollNumber())
        .fatherName(fatherName)
        .motherName(motherName)
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .sectionName(student.getSection().getName())
        .height(studentData.map(MedicalRecords::getHeight).orElse(null))
        .weight(studentData.map(MedicalRecords::getWeight).orElse(null))
        .bloodGroup(studentData.map(MedicalRecords::getBloodGroup).orElse(null))
        .scholasticMandatory(scholasticMandatory)
        .scholasticTotalPercentage(buildScholasticTotalPercentage(getMarksResponse))
        .scholasticTotalGrade(buildScholasticTotalGrade(getMarksResponse))
        .attendance(
            request.attendance() != 0.0
                ? request.attendance()
                : attendance(orgSlug, student.getId()))
        .coScholasticMandatory(buildCoScholasticMandatory(data, gradeSlug))
        .coScholasticOptional(buildCoScholasticOptional(data))
        .remarks(remarks)
        .issueDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")))
        .build();
  }

  public double attendance(String orgSlug, Long studentId) {
    return sectionAttendanceDetailRepository.getAttendancePercentageByStudent(orgSlug, studentId);
  }

  private List<LowerGradeReportCardData> getData(Student student) {
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            List.of("t1", "t2"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    return data;
  }

  private List<Scholar11th12thReportDto.SkillGroup> buildCoScholasticOptional(
      List<LowerGradeReportCardData> data) {
    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();
    var coScholasticAreaResources = getCoScholasticAreaResources();

    List<Scholar11th12thReportDto.SkillGroup> response = new ArrayList<>();

    List<Scholar11th12thReportDto.Skill> healthSkills =
        buildSkillsForCategory(
            coScholasticOptionalData, coScholasticAreaResources, "disciplinary-traits");
    if (!healthSkills.isEmpty()) {
      response.add(
          Scholar11th12thReportDto.SkillGroup.builder()
              .skillName("DISCIPLINARY TRAITS")
              .skill(healthSkills)
              .build());
    } else {
      return Collections.emptyList();
    }
    return response;
  }

  private List<Scholar11th12thReportDto.Skill> buildSkillsForCategory(
      List<LowerGradeReportCardData> coScholasticData,
      List<InterOverAllReportDto.InterTerm1CoScholastic> coScholasticAreaResources,
      String categoryTitle) {

    return coScholasticData.stream()
        .filter(rcd -> categoryTitle.equals(rcd.getSubjectSlug()))
        .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
        .filter(rcd -> rcd.getMarks() != null || rcd.getRemarks() != null)
        .map(
            rcd -> {
              String grade = calculateCoScholasticGrade(rcd.getMarks());
              if (grade == null || grade.isBlank()) {
                grade =
                    Optional.ofNullable(rcd.getRemarks())
                        .filter(r -> r.length() >= 2)
                        .map(r -> r.substring(0, 2))
                        .orElse("");
              }
              return Scholar11th12thReportDto.Skill.builder()
                  .subject(rcd.getSubjectName())
                  .grade(grade)
                  .build();
            })
        .collect(Collectors.toList());
  }

  private String calculateCoScholasticGrade(Double marks) {
    return marks == null || marks == 0
        ? null
        : pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(marks));
  }

  private List<Scholar11th12thReportDto.SkillGroup> buildCoScholasticMandatory(
      List<LowerGradeReportCardData> data, String gradeSlug) {
    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();
    var coScholasticAreaResources = getCoScholasticAreaResources();

    List<Scholar11th12thReportDto.SkillGroup> response = new ArrayList<>();

    List<Scholar11th12thReportDto.Skill> healthSkills =
        buildSkillsForCategory(
            coScholasticOptionalData, coScholasticAreaResources, "health-and-physical-education");
    if (!healthSkills.isEmpty()) {
      response.add(
          Scholar11th12thReportDto.SkillGroup.builder()
              .skillName("HEALTH & PHYSICAL EDUCATION")
              .skill(healthSkills)
              .build());
    }

    List<Scholar11th12thReportDto.Skill> knowledgeSkills =
        buildSkillsForCategory(coScholasticOptionalData, coScholasticAreaResources, "gs");
    if (!knowledgeSkills.isEmpty() && !(gradeSlug.equals("xi") || gradeSlug.equals("xii"))) {
      response.add(
          Scholar11th12thReportDto.SkillGroup.builder()
              .skillName("GENERAL STUDIES")
              .skill(knowledgeSkills)
              .build());
    }

    List<Scholar11th12thReportDto.Skill> workExperienceSkills =
        buildSkillsForCategory(
            coScholasticOptionalData, coScholasticAreaResources, "work-experience");
    if (!workExperienceSkills.isEmpty() && (gradeSlug.equals("xi") || gradeSlug.equals("xii"))) {
      response.add(
          Scholar11th12thReportDto.SkillGroup.builder()
              .skillName("WORK EXPERIENCE")
              .skill(workExperienceSkills)
              .build());
    }
    return response;
  }

  private List<InterOverAllReportDto.InterTerm1CoScholastic> getCoScholasticAreaResources() {

    List<InterOverAllReportDto.InterTerm1CoScholastic> coScholasticAreas = new ArrayList<>();
    try {
      var objectMapper = new ObjectMapper();
      coScholasticAreas =
          objectMapper.readValue(ResourceUtils.asString(resource), new TypeReference<>() {});
      if (Objects.isNull(coScholasticAreas) || coScholasticAreas.isEmpty()) {
        return coScholasticAreas;
      }
    } catch (Exception ex) {
      log.error(
          "Unable to process the resource [inter-term1-co-scholastic.json] from the classpath", ex);
      return coScholasticAreas;
    }
    return coScholasticAreas;
  }

  private Scholar11th12thReportDto.Mark getMarksResponse(
      List<Scholar11th12thReportDto.SubjectMark> scholasticMandatory) {
    if (Objects.isNull(scholasticMandatory) || scholasticMandatory.isEmpty()) {
      return null;
    }
    DoubleStream total =
        scholasticMandatory.stream()
            .map(Scholar11th12thReportDto.SubjectMark::total)
            .flatMap(List::stream)
            .mapToDouble(Scholar11th12thReportDto.Mark::mm);
    var scholasticTotal =
        scholasticMandatory.stream()
            .mapToDouble(
                s -> s.total().stream().mapToDouble(Scholar11th12thReportDto.Mark::mo).sum())
            .sum();
    var totalMarks = (scholasticMandatory.size() * 100);

    return Scholar11th12thReportDto.Mark.builder().mo(scholasticTotal).mm(totalMarks).build();
  }

  private String buildScholasticTotalGrade(Scholar11th12thReportDto.Mark marks) {
    if (Objects.isNull(marks) || Objects.isNull(marks.mo()) || Objects.isNull(marks.mm())) {
      return null;
    }
    if (marks.mo() == 0 || marks.mm() == 0) {
      return "AB";
    }
    return calculateGrade(marks.mo(), marks.mm(), "8point");
  }

  private String buildScholasticTotalPercentage(Scholar11th12thReportDto.Mark marks) {
    var percentage = (marks.mo() / marks.mm()) * 100;
    return String.format("%.1f%%", percentage);
  }

  private List<Scholar11th12thReportDto.SubjectMark> buildScholasticMandatory(
      List<LowerGradeReportCardData> data) {
    var scholasticMandatoryData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var scholasticDataMap =
        scholasticMandatoryData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    List<Scholar11th12thReportDto.SubjectMark> scholasticMandatory = new ArrayList<>();
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var hyeStr = getMarks(List.of("hye"), scholasticData);
          var hyeTotalMarks = getTotalMarks(List.of("hye"), scholasticData);
          var practicalStr = getMarks(List.of("practical"), scholasticData);
          var practicalTotalMarks = getTotalMarks(List.of("practical"), scholasticData);
          Scholar11th12thReportDto.Mark hyeMark = null;
          if (hyeStr != null && !hyeStr.equals("AB") && hyeStr.matches("\\d+(\\.\\d+)?")) {
            double hyeValue = Double.parseDouble(hyeStr);
            double hyeTotal = Double.parseDouble(hyeTotalMarks);
            hyeMark = Scholar11th12thReportDto.Mark.builder().mo(hyeValue).mm(hyeTotal).build();
          } else if (hyeStr != null) {
            hyeMark = Scholar11th12thReportDto.Mark.builder().mo(0.0).mm(80.0).build();
          }

          Scholar11th12thReportDto.Mark practicalMark = null;
          if (practicalStr != null
              && !practicalStr.equals("AB")
              && practicalStr.matches("\\d+(\\.\\d+)?")) {
            double practicalValue = Double.parseDouble(practicalStr);
            double practicalTotal = Double.parseDouble(practicalTotalMarks);
            practicalMark =
                Scholar11th12thReportDto.Mark.builder()
                    .mo(practicalValue)
                    .mm(practicalTotal)
                    .build();
          } else if (practicalStr != null) {
            practicalMark = Scholar11th12thReportDto.Mark.builder().mo(0.0).mm(20.0).build();
          }

          var totalScoredMarks = sumMarks(hyeStr, practicalStr);
          var totalMarks = 100.0;
          var grade = calculateGrade(totalScoredMarks, totalMarks, null);

          var totalMark =
              Scholar11th12thReportDto.Mark.builder().mo(totalScoredMarks).mm(totalMarks).build();

          List<Scholar11th12thReportDto.Mark> hyeList =
              hyeMark != null ? List.of(hyeMark) : Collections.emptyList();

          List<Scholar11th12thReportDto.Mark> practicalList =
              practicalMark != null ? List.of(practicalMark) : Collections.emptyList();

          scholasticMandatory.add(
              Scholar11th12thReportDto.SubjectMark.builder()
                  .subject(subject)
                  .hyexam(hyeList)
                  .practical(practicalList)
                  .total(List.of(totalMark))
                  .grade(grade)
                  .build());
        });
    return scholasticMandatory;
  }

  private String calculateGrade(Double marks, Double totalMarks, String gradeScaleSlug) {
    return marks == null || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScaleSlug == null ? "8point" : gradeScaleSlug,
            BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  public String getTotalMarks(
      List<String> assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    List<LowerGradeReportCardData> data;
    Double total;

    data =
        subjectData.stream().filter(d -> assessmentSlug.contains(d.getAssessmentSlug())).toList();

    if (data.isEmpty()) {
      return null;
    }

    var isAttended = data.stream().filter(d -> d.getIsAttended() == null).toList();
    if (!isAttended.isEmpty()) {
      return null;
    }

    var data1 =
        data.stream().filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended()))).toList();

    if (data1.isEmpty()) {
      return "0.00";
    }

    total =
        data1.stream()
            .filter(datas -> datas.getMarks() != null)
            .mapToDouble(datas -> datas.getSubjectMarks())
            .sum();

    return String.format("%.2f", total);
  }

  public String getMarks(List<String> assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    List<LowerGradeReportCardData> data;
    Double average;

    data =
        subjectData.stream().filter(d -> assessmentSlug.contains(d.getAssessmentSlug())).toList();

    if (data.isEmpty()) {
      return null;
    }

    var isAttended = data.stream().filter(d -> d.getIsAttended() == null).toList();
    if (!isAttended.isEmpty()) {
      return null;
    }

    var data1 =
        data.stream().filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended()))).toList();

    if (data1.isEmpty()) {
      return data.getFirst().getRemarks() == null
          ? "AB"
          : data.getFirst().getRemarks().substring(0, 2).toUpperCase();
    }

    average =
        data1.stream()
            .filter(datas -> datas.getMarks() != null)
            .mapToDouble(datas -> datas.getMarks())
            .average()
            .orElse(0.0);

    return String.format("%.2f", average);
  }

  public Double sumMarks(String... marks) {
    return Arrays.stream(marks)
        .filter(mark -> mark != null && !mark.isEmpty() && mark.matches("\\d+(\\.\\d+)?"))
        .mapToDouble(Double::parseDouble)
        .sum();
  }

  private Scholar11th12thReportDto.Header buildReportCardHeader(User user, Organization org) {
    return Scholar11th12thReportDto.Header.builder()
        .schoolName(org.getName())
        .address("JAMNIWALA ROAD, BADRIPUR, PAONTA SAHIB DISTT. SIRMOUR (H.P.) - 173025")
        .build();
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("11th-12th-scholars-report-card.xml");
  }
}
