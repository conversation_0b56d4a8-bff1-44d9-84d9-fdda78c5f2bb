package com.wexl.dps.reportcard;

import com.wexl.dps.dto.ScholarsReportCardDto;
import com.wexl.dps.learningmilestones.model.*;
import com.wexl.dps.learningmilestones.repository.*;
import com.wexl.retail.erp.attendance.dto.MedicalRecords;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceDetailRepository;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentRepository;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsMetadataStudents;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import com.wexl.retail.subjects.repository.SubjectsMetadataStudentsRepository;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ScholarsReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final OfflineTestScheduleRepository offlineTestScheduleRepository;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final LmrCategoryGradeRepository categoryGradeRepository;
  private final CategoryGradeAttributeRepository lmrCategoryGradeAttributeRepository;
  private final LmrCategoryRepository lmrCategoryRepository;
  private final LmrCategoryAttributeDefinitionRepository lmrCategoryAttributeDefinitionRepository;
  private final LmrStudentDetailRepository lmrStudentDetailRepository;
  private final SubjectsMetadataStudentsRepository subjectsMetadataStudentsRepository;
  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private static final String HYE = "Half Yearly";
  private final SectionAttendanceDetailRepository sectionAttendanceDetailRepository;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var student = user.getStudentInfo();
    var header = buildScholarsHeader(student, org);
    var body = buildScholarsBody(student, org, request);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("scholors-1st-2nd-report-card.xml");
  }

  private ScholarsReportCardDto.Header buildScholarsHeader(Student student, Organization org) {
    Optional<StudentAttributeValueModel> admissionNo =
        getStudentAttributeValue(student, "admission_no");

    return ScholarsReportCardDto.Header.builder()
        .schoolName(org.getName())
        .logo(org.getLogo())
        .address("JAMNIWALA ROAD, BADRIPUR, PAONTA SAHIB DISTT. SIRMOUR (H.P.) - 173025")
        .build();
  }

  private ScholarsReportCardDto.Body buildScholarsBody(
      Student student, Organization org, ReportCardDto.Request request) {
    List<ScholarsReportCardDto.Subject> subjects = new ArrayList<>();
    var studentSubjectMetadata =
        subjectsMetadataStudentsRepository.findByStudentId(student.getId());
    List<SubjectsMetaData> subjectMetadataList =
        studentSubjectMetadata.stream()
            .map(SubjectsMetadataStudents::getSubjectsMetaData)
            .sorted(Comparator.comparing(SubjectsMetaData::getSeqNo))
            .toList();
    var remarks =
        offlineTestScheduleStudentRepository.getAttendanceByTitleAndOrgSlugAndStudentId(
            HYE, org.getSlug(), student.getId());
    var lmrStudentDetails =
        lmrStudentDetailRepository.findAllByOrgSlugAndStudentIdAndTermId(
            org.getSlug(), student.getId(), request.termId());
    Map<Long, String> lmrStudentDetailsMap =
        lmrStudentDetails.stream()
            .collect(
                Collectors.toMap(
                    LmrStudentDetail::getLmrCategoryAttributeId, LmrStudentDetail::getSkillValue));
    for (SubjectsMetaData subjectsMetaData : subjectMetadataList) {
      List<LmrCategoryGrade> lmrCategoryGrades =
          categoryGradeRepository.getAllByGradeSlugAndSubjectMetadataIdAndTerm(
              student.getSection().getGradeSlug(), subjectsMetaData.getId(), request.termId());

      var lmrCategories =
          lmrCategoryRepository.findByIdInAndType(
              lmrCategoryGrades.stream().map(LmrCategoryGrade::getLmrCategoryId).toList(),
              LmrCategoryType.EY_REPORT);
      List<ScholarsReportCardDto.SkillGroup> skillGroups = new ArrayList<>();
      for (LmrCategory lmrCategory :
          lmrCategories.stream().sorted(Comparator.comparing(LmrCategory::getSequence)).toList()) {
        var lmrGradeIds =
            lmrCategoryGrades.stream()
                .filter(lcg -> lcg.getLmrCategoryId().equals(lmrCategory.getId()))
                .map(LmrCategoryGrade::getId)
                .toList();
        var lmrCategoryAttributes =
            lmrCategoryGradeAttributeRepository.findByLmrCategoryGradeIdIn(lmrGradeIds);
        List<ScholarsReportCardDto.Skill> skills = new ArrayList<>();
        for (LmrCategoryGradeAttribute lmrCategoryAttribute : lmrCategoryAttributes) {
          skills.add(
              ScholarsReportCardDto.Skill.builder()
                  .name(lmrCategoryAttribute.getAttributeName())
                  .grade(lmrStudentDetailsMap.get(lmrCategoryAttribute.getId()))
                  .build());
        }
        skillGroups.add(
            ScholarsReportCardDto.SkillGroup.builder()
                .groupName(lmrCategory.getName())
                .skills(skills)
                .build());
      }
      subjects.add(
          ScholarsReportCardDto.Subject.builder()
              .name(subjectsMetaData.getName())
              .term("Term " + request.termId())
              .skillGroups(skillGroups)
              .build());
    }
    var studentData =
        sectionAttendanceDetailRepository.getMedicalRecords(student.getId(), org.getSlug());
    buildHeightAndWeight(request.termId(), subjects, studentData);

    Optional<StudentAttributeValueModel> dateOfBirth =
        getStudentAttributeValue(student, "date_of_birth");
    Optional<StudentAttributeValueModel> bloodGroup =
        getStudentAttributeValue(student, "blood_group");
    Optional<StudentAttributeValueModel> mobileNumber =
        getStudentAttributeValue(student, "mobile_number");
    Optional<StudentAttributeValueModel> address = getStudentAttributeValue(student, "address");

    return ScholarsReportCardDto.Body.builder()
        .name(student.getUserInfo().getFirstName() + " " + student.getUserInfo().getLastName())
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .gradeName(student.getSection().getGradeName())
        .admissionNumber(student.getRollNumber() != null ? student.getRollNumber() : "")
        .fatherName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findAny()
                .map(x -> x.getFirstName() + " " + x.getLastName())
                .orElse(""))
        .motherName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findAny()
                .map(x -> x.getFirstName() + " " + x.getLastName())
                .orElse(""))
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .rollNo(student.getClassRollNumber() != null ? student.getClassRollNumber() : "")
        .bloodGroup(bloodGroup.map(StudentAttributeValueModel::getValue).orElse(""))
        .mobileNumber(mobileNumber.map(StudentAttributeValueModel::getValue).orElse(""))
        .address(address.map(StudentAttributeValueModel::getValue).orElse(""))
        .attendance(
            request.attendance() != 0.0
                ? request.attendance()
                : attendance(org.getSlug(), student.getId()))
        .remarks(remarks.isEmpty() ? "" : remarks.get().getRemarks())
        .issueDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")))
        .subjects(subjects)
        .build();
  }

  public double attendance(String orgSlug, Long studentId) {
    return sectionAttendanceDetailRepository.getAttendancePercentageByStudent(orgSlug, studentId);
  }

  private void buildHeightAndWeight(
      Long termId,
      List<ScholarsReportCardDto.Subject> subjects,
      Optional<MedicalRecords> studentData) {
    String heightValue = studentData.map(MedicalRecords::getHeight).orElse(null);

    String weightValue = studentData.map(MedicalRecords::getWeight).orElse(null);

    List<ScholarsReportCardDto.Skill> healthSkills = new ArrayList<>();

    healthSkills.add(
        ScholarsReportCardDto.Skill.builder().name("HEIGHT (cm)").grade(heightValue).build());

    healthSkills.add(
        ScholarsReportCardDto.Skill.builder().name("WEIGHT (kg)").grade(weightValue).build());

    ScholarsReportCardDto.SkillGroup healthSkillGroup =
        ScholarsReportCardDto.SkillGroup.builder().groupName(null).skills(healthSkills).build();

    subjects.add(
        ScholarsReportCardDto.Subject.builder()
            .name("HEALTH STATUS")
            .term("Term " + termId)
            .skillGroups(List.of(healthSkillGroup))
            .build());
  }

  private List<ScholarsReportCardDto.SkillGroup> buildSkillGroups(
      LmrCategory lmrCategory,
      List<LmrCategoryGradeAttribute> lmrCategoryGradeAttribute,
      Student student,
      Long termId) {
    List<ScholarsReportCardDto.SkillGroup> skillGroups = new ArrayList<>();
    List<ScholarsReportCardDto.Skill> skills = new ArrayList<>();
    for (LmrCategoryGradeAttribute attribute : lmrCategoryGradeAttribute) {
      var lmrStudentDetails =
          lmrStudentDetailRepository.findByStudentIdAndTermIdAndLmrCategoryAttributeId(
              student.getId(), termId, attribute.getId());
      skills.add(
          ScholarsReportCardDto.Skill.builder()
              .name(attribute.getAttributeName())
              .grade(lmrStudentDetails.map(LmrStudentDetail::getSkillValue).orElse(null))
              .build());

      skillGroups.add(
          ScholarsReportCardDto.SkillGroup.builder()
              .groupName(lmrCategory.getName())
              .skills(skills)
              .build());
    }
    return skillGroups;
  }
}
