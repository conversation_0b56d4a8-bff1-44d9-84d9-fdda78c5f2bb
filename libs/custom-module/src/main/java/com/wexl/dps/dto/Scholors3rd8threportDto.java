package com.wexl.dps.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record Scholors3rd8threportDto() {
  @Builder
  public record Model(Header header, Body body) {}

  @Builder
  public record Header(String schoolName, String address) {}

  @Builder
  public record Body(
      String name,
      String admissionNumber,
      String rollNo,
      String startYear,
      String endYear,
      String fatherName,
      String motherName,
      String sectionName,
      String gradeSlug,
      String height,
      String dateOfBirth,
      String bloodGroup,
      String weight,
      List<ScholasticMandatory> scholosticMandatory,
      String scholosticTotalPercentage,
      String scholosticTotalGrade,
      List<ScholasticOptional> scholosticOptional,
      @JsonProperty("attendance") Double attendance,
      List<CoScholastic> coScholosticMandatory,
      List<CoScholastic> coScholosticOptional,
      @JsonProperty("remarks") String remarks,
      String issueDate,
      String classTeacherName) {}

  @Builder
  public record ScholasticMandatory(
      String subject,
      String pt,
      String ma,
      String portfolio,
      String se,
      String hye,
      double total,
      String grade) {}

  @Builder
  public record ScholasticOptional(
      String subject, String theory, String practical, double total, String grade) {}

  @Builder
  public record CoScholastic(String skillName, String term, List<Skill> skill) {}

  @Builder
  public record Skill(String subject, String grade) {}

  @Builder
  public record Marks(Double marksScored, Double totalMarks) {}
}
