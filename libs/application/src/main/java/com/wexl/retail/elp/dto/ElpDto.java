package com.wexl.retail.elp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.dto.QuestionDto;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public record ElpDto() {
  @Builder
  public record Chapter(
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      List<Topic> topics,
      @JsonProperty("test_def_id") Long testDefinitionId,
      @JsonProperty("student_task_inst") List<StudentTaskInst> StudentTaskInst) {}

  @Builder
  public record Topic(
      @JsonProperty("topic_name") String topicName,
      @JsonProperty("topic_slug") String topicSlug,
      @JsonProperty("task_id") Long taskId,
      @JsonProperty("due_date") Long dueDate,
      @JsonProperty("attendance_percentage") Double attendancePercentage) {}

  @Builder
  public record StudentTaskInst(
      @JsonProperty("topic_name") String topicName,
      @JsonProperty("topic_slug") String topicSlug,
      @JsonProperty("task_id") Long taskId,
      @JsonProperty("task_inst_id") Long taskInstId,
      @JsonProperty("status") TaskStatus status,
      @JsonProperty("exam_id") Long examId,
      @JsonProperty("task_knowledge_meter") Double taskKm,
      @JsonProperty("remarks") String remarks,
      @JsonProperty("due_date") Long dueDate) {}

  @Builder
  public record TriggerRequest(
      @JsonProperty("grade_slug") String gradeSlug, @JsonProperty("task_ids") List<Long> taskIds) {}

  @Builder
  public record Initialize(
      @JsonProperty("org_slug") String orgSlug, @JsonProperty("board_slug") String boardSlug) {}

  @Builder
  public record Request(
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("chapter_name") String chapterName) {}

  @Builder
  public record StudentAnswerRequest(
      @JsonProperty("exam_id") Long examId,
      List<QuestionDto.StudentQuestionStatusResponse> questions) {}

  @Builder
  public record StudentExamAnswerRequest(
      @JsonProperty("exam_id") Long examId, QuestionDto.StudentQuestionStatusResponse questions) {}

  @Builder
  public record TaskResponse(
      @JsonProperty("summary") Summary summary, @JsonProperty("data") List<Data> data) {}

  @Builder
  public record Summary(
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("org_slug") String orgSlug,
      @JsonProperty("org_name") String orgName,
      @JsonProperty("teacher_name") String teacherName,
      @JsonProperty("date") Long date,
      @JsonProperty("topic_summary") List<TopicSummary> topicSummary) {}

  @Builder
  public record Data(
      @JsonProperty("name") String name,
      @JsonProperty("user_id") String userId,
      @JsonProperty("section_name") String sectionName,
      TaskStatus status,
      @JsonProperty("student_tasks") List<StudentTaskInst> studentTaskInsts) {}

  @Builder
  public record TopicSummary(
      @JsonProperty("topic_name") String name,
      @JsonProperty("not_attempted_count") Long notAttemptedCount,
      @JsonProperty("question_count") Long questionCount,
      @JsonProperty("average") Double average) {}

  @Builder
  public record Remarks(@JsonProperty("remarks") String remarks) {}

  @Builder
  public record ConfigElp(
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("test_Type") TestType testType) {}

  @Builder
  public record SwitchElpStudentRequest(
      @NotNull @JsonProperty("user_name") String userName,
      @NotNull @JsonProperty("student_passcode") String passcode,
      @NotNull String password) {}

  @Builder
  public record ContentRequest(@JsonProperty("request") Request request) {}

  @Builder
  public record ElpTaskMigrationRequest(
      @JsonProperty("target_elp_slug") String newElpSlug,
      @JsonProperty("org_slug") String orgSlug,
      @JsonProperty("source_elp_slug") String oldElpSlug) {}

  @Builder
  public record TeacherElps(String boardSlug, String boardName, List<ElpGrades> elpGrades) {}

  @Builder
  public record ElpGrades(String gradeSlug, String gradeName) {}

  @Builder
  public record PhoneticsResponseList(
      @JsonProperty("phonetics") List<PhoneticsResponse> phonetics) {}

  @Builder
  public record PhoneticsResponse(
      @JsonProperty("word") String word, @JsonProperty("audio") String audio) {}

  public record PhoneticsData(List<SoundData> data) {}

  public record SoundData(
      String sound,
      String audio,
      String introduction,
      List<ExampleWord> example_words,
      List<PracticeWord> more_words_for_practice,
      List<Sentence> sentences,
      String uuid) {}

  public record ExampleWord(String word, String audio) {}

  public record PracticeWord(String word, String phonetics, String audio) {}

  public record Sentence(String sentence, String audio) {}

  @Builder
  public record SmartBoardElpQuestionResponse(QuestionDto.QuestionResponse questionResponse) {}

  @Builder
  public record StudentElpResponse(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_auth_id") String studentAuthId,
      @JsonProperty("question_uuid") String questionUuid,
      @JsonProperty("exam_id") Long examId) {}

  public record AttemptQuestionRequest(
      @JsonProperty("student_Id") String studentAuthId,
      @JsonProperty("student_answer_request")
          QuestionDto.StudentQuestionStatusResponse studentQuestionStatusResponse) {}

  @Builder
  public record SmartBoardAttemptResponse(
      @JsonProperty("exam_id") Long examId,
      @JsonProperty("student_answer_request")
          QuestionDto.ValidateAnswerResponse validateAnswerResponse) {}

  @Builder
  public record PbqQuestionAnswerValidationRequest(
      String uuid, String pbqMcqUuid, Integer answer) {}
}
