package com.wexl.retail.qpgen.controller;

import com.wexl.retail.qpgen.dto.QPGenProV2Dto;
import com.wexl.retail.qpgen.service.QpGenProV2Service;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/qp-gen-pro-v2")
@RequiredArgsConstructor
public class QPGenProV2Controller {

  private final QpGenProV2Service qpGenProV2Service;

  @PostMapping("/question-summary")
  public List<QPGenProV2Dto.QuestionSummaryResponse> getQuestionSummary(
      @PathVariable String orgSlug, @RequestBody QPGenProV2Dto.QuestionSummaryRequest request) {
    return qpGenProV2Service.getQuestionSummary(orgSlug, request);
  }

  @PostMapping()
  public void saveQpGenProV2(
      @PathVariable String orgSlug, @RequestBody QPGenProV2Dto.Request request) {
    qpGenProV2Service.saveQpGenProV2(orgSlug, request);
  }

  @GetMapping("/{qpGenProV2Id}/question-summary")
  public List<QPGenProV2Dto.QuestionSummaryResponse> getQuestionSummaryV2(
      @PathVariable Long qpGenProV2Id) {
    return qpGenProV2Service.getQuestionSummaryV2(qpGenProV2Id);
  }

  @PutMapping("/{qpGenProId}")
  public void editQpGenProV2(
      @PathVariable String orgSlug,
      @PathVariable Long qpGenProId,
      @RequestBody QPGenProV2Dto.Request editRequest) {
    qpGenProV2Service.editQpGenProV2(orgSlug, editRequest, qpGenProId);
  }

  @GetMapping("/{qpGenProId}/question-chapter-selections")
  public List<QPGenProV2Dto.QuestionSummaryResponse> getQuestionChapterSelections(
      @PathVariable String orgSlug, @PathVariable Long qpGenProId) {
    return qpGenProV2Service.getQuestionChapterSelections(orgSlug, qpGenProId);
  }
}
