package com.wexl.retail.erp.attendance.repository;

import com.wexl.retail.erp.attendance.domain.SectionAttendanceDetails;
import com.wexl.retail.erp.attendance.dto.AttendanceSummary;
import com.wexl.retail.erp.attendance.dto.MedicalRecords;
import com.wexl.retail.erp.attendance.dto.OrgLevelAttendance;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SectionAttendanceDetailRepository
    extends JpaRepository<SectionAttendanceDetails, Long> {

  @Query(
      value = "select * from section_attendance_details where section_attendance_id =:attendanceId",
      nativeQuery = true)
  List<SectionAttendanceDetails> findBySectionAttendanceId(Long attendanceId);

  @Query(
      value =
          """
                      with userdetails as (select  * from section_attendance
                      where org_id = :orgId and date_id <= :startDate and date_id >=:endDate)
                       select a.id as attendanceId, extract(epoch from date(to_timestamp(cast(date_id as text), 'yyyyMMdd')))  * 1000 as dateId ,s.name as sectionName,s.grade_name as gradeName,sum(case when d.attendance_status= 'present' then 1 else 0 end) presentCount,
                       sum(case when d.attendance_status= 'absent' then 1 else 0 end) absentCount,
                       sum(case when d.attendance_status= 'leave' then 1 else 0 end) leaveCount,
                       sum(case when d.attendance_status= 'late_comer' then 1 else 0 end) lateComerCount,
                       sum(case when d.afternoon_attendance_status= 'half_day' then 1 else 0 end) halfDayCount,
                       SUM(CASE WHEN d.attendance_status= 'ptm' then 1 else 0 end) ptmCount,
                       SUM(CASE WHEN d.attendance_status= 'nid' then 1 else 0 end) nidCount,
                       s.id as sectionId,cast(s.uuid as varchar) as sectionUuid, case when a.is_holiday = 0 then 'completed' when a.is_holiday = 1 then 'notcompleted' else 'holiday' end as status from userdetails a left outer join section_attendance_details d on a.id = d.section_attendance_id
                       join sections s on s.id = a.section_id where a.section_id in (:sectionIds) group by a.date_id,a.section_id,a.is_holiday,s.grade_name,s.name,a.id,s.id,s.uuid order by a.date_id DESC,status, length(s.name ),s.name ASC limit :limit""",
      nativeQuery = true)
  List<AttendanceSummary> getAttendanceSummary(
      Long orgId, Integer startDate, Integer endDate, List<Long> sectionIds, int limit);

  @Query(
      value =
          """
                              with userdetails as (select  * from section_attendance
                              where org_id = :orgId and date_id <= :startDate and date_id >=:endDate)
                               select a.id as attendanceId, extract(epoch from date(to_timestamp(cast(date_id as text), 'yyyyMMdd')))  * 1000 as dateId ,
                               s.name as sectionName,s.grade_name as gradeName,sum(case when d.afternoon_attendance_status = 'present' then 1 else 0 end) presentCount,
                               sum(case when d.afternoon_attendance_status= 'absent' then 1 else 0 end) absentCount,
                               sum(case when d.attendance_status= 'late_comer' then 1 else 0 end) lateComerCount,
                               sum(case when d.afternoon_attendance_status= 'late_comer' then 1 else 0 end) lateComerCount,
                               sum(case when d.afternoon_attendance_status= 'half_day' then 1 else 0 end) halfDayCount,
                               s.id as sectionId,cast(s.uuid as varchar) as sectionUuid, case when a.is_holiday = 0 then 'completed' when a.is_holiday = 1 then 'notcompleted' else 'holiday' end as status from userdetails a left outer join section_attendance_details d on a.id = d.section_attendance_id
                               join sections s on s.id = a.section_id where a.section_id in (:sectionIds) group by a.date_id,a.section_id,a.is_holiday,s.grade_name,s.name,a.id,s.id,s.uuid order by a.date_id DESC,status, length(s.name ),s.name ASC limit :limit""",
      nativeQuery = true)
  List<AttendanceSummary> getAfternoonAttendanceSummary(
      Long orgId, Integer startDate, Integer endDate, List<Long> sectionIds, int limit);

  @Query(
      value =
          """
                      select  a.date_id as dateId ,s.name as sectionName , s.id as sectionId,
                                       sum(case when d.attendance_status= 'present' then 1 else 0 end) presentCount,
                                       sum(case when d.attendance_status= 'absent' then 1 else 0 end) absentCount,
                                       sum(case when d.afternoon_attendance_status= 'late_comer' then 1 else 0 end) lateComerCount,
                                       sum(case when d.afternoon_attendance_status= 'half_day' then 1 else 0 end) halfDayCount,
                                       sum(case when d.attendance_status= 'leave' then 1 else 0 end) leaveCount,
                                       sum(case when d.attendance_status= 'ptm' then 1 else 0 end) ptmCount,
                                       sum(case when d.attendance_status= 'nid' then 1 else 0 end) nidCount,
                                       cast(s.uuid as varchar) as sectionUuid  from section_attendance a
                                       inner join section_attendance_details d on a.id = d.section_attendance_id
                                       inner join sections s on s.id = a.section_id where  s.grade_slug in(:grades)\s
                                       and s.organization = :orgSlug and date_id = :date
                                       group by a.date_id,s.name,a.id ,s.uuid ,sectionId
                                       order by presentCount DESC""",
      nativeQuery = true)
  List<AttendanceSummary> getSectionWiseAttendanceReport(
      String orgSlug, Integer date, String grades);

  @Query(
      value =
          """
                      select  a.date_id as dateId ,s.name as sectionName , s.id as sectionId,
                                       sum(case when d.attendance_status= 'present' then 1 else 0 end) presentCount,
                                       sum(case when d.attendance_status= 'absent' then 1 else 0 end) absentCount,
                                       sum(case when d.afternoon_attendance_status= 'late_comer' then 1 else 0 end) lateComerCount,
                                       sum(case when d.afternoon_attendance_status= 'half_day' then 1 else 0 end) halfDayCount,
                                       sum(case when d.attendance_status= 'leave' then 1 else 0 end) leaveCount,
                                       sum(case when d.attendance_status= 'ptm' then 1 else 0 end) ptmCount,
                                       sum(case when d.attendance_status= 'nid' then 1 else 0 end) nidCount,
                                       cast(s.uuid as varchar) as sectionUuid  from section_attendance a
                                       inner join section_attendance_details d on a.id = d.section_attendance_id
                                       inner join sections s on s.id = a.section_id
                                       inner join students st on st.id = d.student_id
                                       where st.id in (:studentIds)
                                       and s.organization = :orgSlug and date_id = :date
                                       group by a.date_id,s.name,a.id ,s.uuid ,sectionId
                                       order by presentCount DESC""",
      nativeQuery = true)
  List<AttendanceSummary> getSectionWiseAfternoonAttendanceReportForCoordinator(
      String orgSlug, Integer date, List<Long> studentIds);

  @Query(
      value =
          """
                      select s.grade_slug as gradeSlug, s.grade_name as gradeName,
                         sum(case when d.attendance_status= 'present' then 1 else 0 end) presentCount,
                         sum(case when d.attendance_status= 'absent' then 1 else 0 end) absentCount,
                         sum(case when d.afternoon_attendance_status= 'late_comer' then 1 else 0 end) lateComerCount,
                         sum(case when d.afternoon_attendance_status= 'half_day' then 1 else 0 end) halfDayCount,
                         sum(case when d.attendance_status= 'leave' then 1 else 0 end) leaveCount,
                         sum(case when d.attendance_status= 'ptm' then 1 else 0 end) ptmCount,
                         sum(case when d.attendance_status= 'nid' then 1 else 0 end) nidCount
                         from section_attendance a
                         inner join section_attendance_details d on a.id = d.section_attendance_id
                         inner join sections s on s.id = a.section_id where  s.grade_slug in (:grades)
                         and s.organization = :org  and date_id = :dateId\s
                         group by s.grade_slug , s.grade_name
                         order by presentCount DESC""",
      nativeQuery = true)
  List<AttendanceSummary> getGradeWiseAttendanceReport(
      String org, Integer dateId, List<String> grades);

  @Query(
      value =
          """
                      select  a.date_id as dateId ,s.name as sectionName , s.id as sectionId,
                                       sum(case when d.afternoon_attendance_status= 'present' then 1 else 0 end) presentCount,
                                       sum(case when d.afternoon_attendance_status= 'absent' then 1 else 0 end) absentCount,
                                       sum(case when d.afternoon_attendance_status= 'leave' then 1 else 0 end) leaveCount,
                                       sum(case when d.afternoon_attendance_status= 'late_comer' then 1 else 0 end) lateComerCount,
                                       sum(case when d.afternoon_attendance_status= 'half_day' then 1 else 0 end) halfDayCount,
                                       sum(case when d.attendance_status= 'ptm' then 1 else 0 end) ptmCount,
                                       sum(case when d.attendance_status= 'nid' then 1 else 0 end) nidCount,
                                       cast(s.uuid as varchar) as sectionUuid  from section_attendance a
                                       inner join section_attendance_details d on a.id = d.section_attendance_id
                                       inner join sections s on s.id = a.section_id where  s.grade_slug in(:grades)
                                       and s.organization = :orgSlug and date_id = :date
                                       group by a.date_id,s.name,a.id ,s.uuid ,sectionId
                                       order by presentCount DESC""",
      nativeQuery = true)
  List<AttendanceSummary> getSectionWiseAfternoonAttendanceReport(
      String orgSlug, Integer date, String grades);

  @Query(
      value =
          """
                      select  a.date_id as dateId ,s.name as sectionName , s.id as sectionId,
                                       sum(case when d.afternoon_attendance_status= 'present' then 1 else 0 end) presentCount,
                                       sum(case when d.afternoon_attendance_status= 'absent' then 1 else 0 end) absentCount,
                                       sum(case when d.afternoon_attendance_status= 'leave' then 1 else 0 end) leaveCount,
                                       sum(case when d.afternoon_attendance_status= 'late_comer' then 1 else 0 end) lateComerCount,
                                       sum(case when d.afternoon_attendance_status= 'half_day' then 1 else 0 end) halfDayCount,
                                       sum(case when d.attendance_status= 'ptm' then 1 else 0 end) ptmCount,
                                       sum(case when d.attendance_status= 'nid' then 1 else 0 end) nidCount,
                                       cast(s.uuid as varchar) as sectionUuid  from section_attendance a
                                       inner join section_attendance_details d on a.id = d.section_attendance_id
                                       inner join sections s on s.id = a.section_id
                                       inner join students st on st.id = d.student_id
                                       where st.id in (:studentIds)
                                       and s.organization = :orgSlug and date_id = :date
                                       group by a.date_id,s.name,a.id ,s.uuid ,sectionId
                                       order by presentCount DESC""",
      nativeQuery = true)
  List<AttendanceSummary> getSectionWiseAttendanceReportForCoordinator(
      String orgSlug, Integer date, List<Long> studentIds);

  @Query(
      value =
          """
                              select s.grade_slug as gradeSlug, s.grade_name as gradeName,
                                 sum(case when d.afternoon_attendance_status= 'present' then 1 else 0 end) presentCount,
                                 sum(case when d.afternoon_attendance_status= 'absent' then 1 else 0 end) absentCount,
                                 sum(case when d.afternoon_attendance_status= 'leave' then 1 else 0 end) leaveCount,
                                 sum(case when d.afternoon_attendance_status= 'late_comer' then 1 else 0 end) lateComerCount,
                                 sum(case when d.afternoon_attendance_status= 'half_day' then 1 else 0 end) halfDayCount,
                                 sum(case when d.attendance_status= 'ptm' then 1 else 0 end) ptmCount,
                                 sum(case when d.attendance_status= 'nid' then 1 else 0 end) nidCount
                                 from section_attendance a
                                 inner join section_attendance_details d on a.id = d.section_attendance_id
                                 inner join sections s on s.id = a.section_id where  s.grade_slug in (:grades)
                                 and s.organization = :org  and date_id = :dateId
                                 group by s.grade_slug , s.grade_name
                                 order by presentCount DESC""",
      nativeQuery = true)
  List<AttendanceSummary> getGradeWiseAfternoonAttendanceReport(
      String org, Integer dateId, List<String> grades);

  @Query(
      value =
          """
                          select count(sa.*) from section_attendance sa join section_attendance_details sad  on sa.id=sad.section_attendance_id
                          join orgs o  on o.id = org_id  where o.slug = :orgSlug
                          and is_holiday = 0 and sad.student_id  = :studentId and sad.attendance_status  = 'present'
                          and sa.date_id >= :startDate and sa.date_id <= :endDate """,
      nativeQuery = true)
  Integer getTotalWorkingDays(String orgSlug, Long studentId, Long startDate, Long endDate);

  @Query(
      value =
          """
                          select count(sa.*) from section_attendance sa join section_attendance_details sad  on sa.id=sad.section_attendance_id
                          join orgs o  on o.id = org_id  where o.slug = :orgSlug
                          and is_holiday = 0 and sad.student_id  = :studentId
                          and sa.date_id >= :startDate and sa.date_id <= :endDate """,
      nativeQuery = true)
  Integer getPresentDays(String orgSlug, Long studentId, Long startDate, Long endDate);

  @Query(
      value =
          """
                  select
                  	sad.student_id as studentId,
                  	u.first_name as firstName,
                  	u.last_name as lastName,
                  	s.name as section,
                  	s.board_slug as boardSlug,
                  	s.board_name as boardName,
                  	s.grade_slug as gradeSlug,
                  	s.grade_name as gradeName,
                  	s2.class_roll_number as classRollNumber
                  from
                  	section_attendance sa
                  inner join sections s on
                  	sa.section_id = s.id
                  inner join calender_details cd on
                  	cd.date_id = sa.date_id
                  inner join section_attendance_details sad on
                  	sad.section_attendance_id = sa.id
                  inner join students s2 on sad.student_id = s2.id
                  inner join users u on u.id = s2.user_id
                  where
                  	s.organization = :orgSlug
                  	and sa.date_id = :dateId and s.status='ACTIVE' and (
                    CASE
                      WHEN :session = 'morning' THEN sad.attendance_status
                      WHEN :session = 'afternoon' THEN sad.afternoon_attendance_status
                    END
                  ) = :status
                  """,
      nativeQuery = true)
  public List<OrgLevelAttendance> getOrgLevelAttendance(
      String orgSlug, Integer dateId, String session, String status);

  @Query(
      value =
          """
    SELECT
        t.date_id AS dateId,
        COUNT(CASE WHEN t.attendance_status = 'present' THEN 1 END) AS presentCount,
        COUNT(CASE WHEN t.attendance_status = 'absent' THEN 1 END) AS absentCount,
        COUNT(CASE WHEN t.attendance_status = 'late_comer' THEN 1 END) AS lateComerCount,
        COUNT(CASE WHEN t.afternoon_attendance_status = 'half_day' THEN 1 END) AS halfDayCount,
        COUNT(CASE WHEN t.attendance_status = 'leave' THEN 1 END) AS leaveCount,
        COUNT(CASE WHEN t.attendance_status = 'ptm' THEN 1 END) AS ptmCount,
        COUNT(CASE WHEN t.attendance_status = 'nid' THEN 1 END) AS nidCount
    FROM (
        SELECT DISTINCT
            a.date_id,
            d.student_id,
            d.attendance_status,
            d.afternoon_attendance_status
        FROM section_attendance a
        JOIN section_attendance_details d ON a.id = d.section_attendance_id
        WHERE a.org_id = :orgId AND a.date_id = :date
    ) t
    GROUP BY t.date_id
    """,
      nativeQuery = true)
  List<AttendanceSummary> getAttendanceSummaryBetweenDates(Long orgId, Integer date);

  @Query(
      value =
          """
    select
        sum(case when d.attendance_status = 'present' then 1 else 0 end) as presentCount,
        sum(case when d.attendance_status = 'late_comer' then 1 else 0 end) as lateComerCount,
        sum(case when d.afternoon_attendance_status = 'half_day' then 1 else 0 end) as halfDayCount,
        sum(case when d.attendance_status= 'nid' then 1 else 0 end) nidCount
    from section_attendance a
    join section_attendance_details d on a.id = d.section_attendance_id
    join orgs o on o.id = a.org_id
    where o.slug = :orgSlug and d.student_id = :studentId
""",
      nativeQuery = true)
  Double getAttendancePercentageByStudent(String orgSlug, Long studentId);

  @Query(
      value =
          """
                          select
                             date_of_birth as dateOfBirth,
                              height_cm as height,
                              weight_kg as weight,
                              blood_group as bloodGroup
                          from medical_histories
                          where student_id = :student and org_slug = :orgSlug;
                           """,
      nativeQuery = true)
  Optional<MedicalRecords> getMedicalRecords(Long student, String orgSlug);
}
